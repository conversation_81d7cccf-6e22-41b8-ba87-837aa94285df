# Word Search Service

一个基于Spring Boot和Elasticsearch的词汇搜索服务，提供强大的全文搜索功能。

## 项目特性

- **Spring Boot 2.7.18** - 支持Java 8的最新稳定版本
- **Elasticsearch 7.17.x** - 分布式搜索引擎
- **Log4j2日志框架** - 高性能异步日志记录
- **Lombok集成** - 简化代码，减少样板代码
- **RESTful API** - 标准的REST接口设计
- **全文搜索** - 支持中文分词和高亮显示
- **多字段搜索** - 支持标题、内容、分类等多字段搜索
- **分页查询** - 支持分页和排序
- **性能监控** - 自动记录API和搜索性能
- **异常处理** - 统一的异常处理机制
- **单元测试** - 完整的测试覆盖

## 技术栈

- **后端框架**: Spring Boot 2.7.18
- **搜索引擎**: Elasticsearch 7.17.x
- **日志框架**: Log4j2 2.17.2 (异步日志)
- **代码简化**: Lombok 1.18.24
- **构建工具**: Maven 3.9.x
- **Java版本**: JDK 1.8+
- **测试框架**: JUnit 5, Mockito
- **文档工具**: Spring Boot Actuator

## 项目结构

```
src/
├── main/
│   ├── java/com/howbuy/ai/
│   │   ├── WordSearchServiceApplication.java  # 主应用类
│   │   ├── config/                           # 配置类
│   │   │   ├── ElasticsearchConfig.java      # ES配置
│   │   │   ├── WebConfig.java                # Web配置
│   │   │   └── DataInitializer.java          # 数据初始化
│   │   ├── controller/                       # 控制器
│   │   │   ├── WordSearchController.java     # 搜索API
│   │   │   └── DocumentController.java       # 文档管理API
│   │   ├── service/                          # 服务层
│   │   │   └── WordSearchService.java        # 搜索服务
│   │   ├── repository/                       # 数据访问层
│   │   │   └── WordDocumentRepository.java   # ES仓库
│   │   ├── entity/                           # 实体类
│   │   │   └── WordDocument.java             # 文档实体
│   │   ├── dto/                              # 数据传输对象
│   │   │   ├── SearchRequest.java            # 搜索请求
│   │   │   └── ApiResponse.java              # API响应
│   │   └── exception/                        # 异常处理
│   │       └── GlobalExceptionHandler.java   # 全局异常处理器
│   └── resources/
│       └── application.yml                   # 应用配置
└── test/                                     # 测试代码
```

## 快速开始

### 1. 环境要求

- JDK 1.8+
- Maven 3.6+
- Elasticsearch 7.17.x

### 2. 启动Elasticsearch

```bash
# 使用Docker启动Elasticsearch
docker run -d \
  --name elasticsearch \
  -p 9200:9200 \
  -p 9300:9300 \
  -e "discovery.type=single-node" \
  -e "ES_JAVA_OPTS=-Xms512m -Xmx512m" \
  elasticsearch:7.17.15
```

### 3. 编译和运行

```bash
# 克隆项目
git clone <repository-url>
cd word-search-service

# 编译项目
mvn clean compile

# 运行测试
mvn test

# 启动应用
mvn spring-boot:run
```

### 4. 验证服务

访问健康检查端点：
```bash
curl http://localhost:8080/api/actuator/health
```

## API文档

### 搜索API

#### 1. 关键词搜索
```http
GET /api/v1/search?keyword=Spring&page=0&size=10
```

#### 2. 高级搜索
```http
POST /api/v1/search/advanced
Content-Type: application/json

{
  "keyword": "Spring Boot",
  "title": "入门",
  "category": "技术文档",
  "page": 0,
  "size": 10
}
```

#### 3. 分类查找
```http
GET /api/v1/search/category/技术文档?page=0&size=10
```

#### 4. 标签查找
```http
GET /api/v1/search/tag/Java
```

#### 5. 获取所有文档
```http
GET /api/v1/search/all?page=0&size=10
```

#### 6. 获取文档详情
```http
GET /api/v1/search/document/{id}
```

### 文档管理API

#### 1. 创建文档
```http
POST /api/v1/documents
Content-Type: application/json

{
  "title": "新文档标题",
  "content": "文档内容...",
  "category": "分类",
  "author": "作者",
  "tags": ["标签1", "标签2"]
}
```

#### 2. 更新文档
```http
PUT /api/v1/documents/{id}
Content-Type: application/json

{
  "title": "更新后的标题",
  "content": "更新后的内容..."
}
```

#### 3. 删除文档
```http
DELETE /api/v1/documents/{id}
```

## 配置说明

### application.yml配置

```yaml
# 服务端口
server:
  port: 8080

# Elasticsearch配置
spring:
  elasticsearch:
    uris: http://localhost:9200
    username:
    password:
    connection-timeout: 10s
    socket-timeout: 30s

# 自定义配置
word-search:
  elasticsearch:
    index-name: word_documents
    max-results: 100
    highlight-enabled: true
```

### Log4j2日志配置

项目使用Log4j2提供高性能的异步日志记录：

**主要特性**:
- 异步日志记录 (使用Disruptor)
- 多种输出格式 (控制台、文件、JSON)
- 自动日志滚动和压缩
- 性能监控日志
- 结构化日志支持

**日志文件**:
```
logs/
├── word-search-service.log          # 主应用日志
├── word-search-service-error.log    # 错误日志
├── word-search-service-perf.log     # 性能日志
└── word-search-service-json.log     # JSON格式日志
```

详细配置请参考: [日志配置指南](docs/logging-guide.md)

### 代码格式化

项目使用Google Java Format确保代码风格一致性：

```bash
# 检查代码格式
./scripts/format-code.sh check

# 格式化代码
./scripts/format-code.sh format
```

详细配置请参考: [代码格式化指南](docs/code-formatting-guide.md)

## 📚 文档

完整的项目文档请查看 [docs/](docs/) 目录：

- **[API使用指南](docs/financial-product-search-guide.md)** - 金融产品搜索API详细说明
- **[代码格式化指南](docs/code-formatting-guide.md)** - Google Java Format使用指南
- **[日志配置指南](docs/logging-guide.md)** - Log4j2日志系统配置
- **[Elasticsearch配置指南](docs/elasticsearch-config-migration-guide.md)** - ES配置现代化迁移
- **[格式化脚本最佳实践](docs/format-script-best-practices.md)** - 开发工具最佳实践

## 开发指南

### 添加新的搜索功能

1. 在`FinancialProductRepository`中添加查询方法
2. 在`FinancialProductService`中实现业务逻辑
3. 在`FinancialProductController`中添加REST端点
4. 编写相应的单元测试

### 自定义Elasticsearch映射

修改`FinancialProduct`实体类中的`@Field`注解来自定义字段映射。

## 故障排除

### 常见问题

1. **Elasticsearch连接失败**
   - 检查Elasticsearch是否正常运行
   - 验证连接配置是否正确

2. **编译错误**
   - 确保使用Java 8+
   - 检查Maven依赖是否正确下载

3. **测试失败**
   - 确保测试环境配置正确
   - 检查Mock对象设置

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证。
