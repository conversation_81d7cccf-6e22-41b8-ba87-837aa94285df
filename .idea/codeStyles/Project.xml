<?xml version="1.0" encoding="UTF-8"?>
<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <option name="OTHER_INDENT_OPTIONS">
      <value>
        <option name="INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
      </value>
    </option>
    <option name="RIGHT_MARGIN" value="100" />
    <option name="WRAP_WHEN_TYPING_REACHES_RIGHT_MARGIN" value="true" />
    <option name="FORMATTER_TAGS_ENABLED" value="true" />
    <JavaCodeStyleSettings>
      <option name="ANNOTATION_PARAMETER_WRAP" value="1" />
      <option name="ALIGN_MULTILINE_ANNOTATION_PARAMETERS" value="true" />
      <option name="CLASS_COUNT_TO_USE_IMPORT_ON_DEMAND" value="999" />
      <option name="NAMES_COUNT_TO_USE_IMPORT_ON_DEMAND" value="999" />
      <option name="PACKAGES_TO_USE_IMPORT_ON_DEMAND">
        <value />
      </option>
      <option name="IMPORT_LAYOUT_TABLE">
        <value>
          <package name="" withSubpackages="true" static="true" />
          <emptyLine />
          <package name="" withSubpackages="true" static="false" />
        </value>
      </option>
      <option name="JD_ALIGN_PARAM_COMMENTS" value="false" />
      <option name="JD_ALIGN_EXCEPTION_COMMENTS" value="false" />
      <option name="JD_P_AT_EMPTY_LINES" value="false" />
      <option name="JD_KEEP_EMPTY_PARAMETER" value="false" />
      <option name="JD_KEEP_EMPTY_EXCEPTION" value="false" />
      <option name="JD_KEEP_EMPTY_RETURN" value="false" />
    </JavaCodeStyleSettings>
    <codeStyleSettings language="JAVA">
      <option name="RIGHT_MARGIN" value="100" />
      <option name="KEEP_LINE_BREAKS" value="false" />
      <option name="KEEP_FIRST_COLUMN_COMMENT" value="false" />
      <option name="KEEP_CONTROL_STATEMENT_IN_ONE_LINE" value="false" />
      <option name="KEEP_BLANK_LINES_IN_DECLARATIONS" value="1" />
      <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
      <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="0" />
      <option name="BLANK_LINES_BEFORE_PACKAGE" value="0" />
      <option name="BLANK_LINES_AFTER_PACKAGE" value="1" />
      <option name="BLANK_LINES_BEFORE_IMPORTS" value="1" />
      <option name="BLANK_LINES_AFTER_IMPORTS" value="1" />
      <option name="BLANK_LINES_AROUND_CLASS" value="1" />
      <option name="BLANK_LINES_AROUND_FIELD" value="0" />
      <option name="BLANK_LINES_AROUND_METHOD" value="1" />
      <option name="BLANK_LINES_BEFORE_METHOD_BODY" value="0" />
      <option name="BLANK_LINES_AROUND_FIELD_IN_INTERFACE" value="0" />
      <option name="BLANK_LINES_AROUND_METHOD_IN_INTERFACE" value="1" />
      <option name="BLANK_LINES_AFTER_CLASS_HEADER" value="0" />
      <option name="BLANK_LINES_AFTER_ANONYMOUS_CLASS_HEADER" value="0" />
      <option name="ALIGN_MULTILINE_CHAINED_METHODS" value="true" />
      <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
      <option name="ALIGN_MULTILINE_PARAMETERS_IN_CALLS" value="false" />
      <option name="ALIGN_MULTILINE_RESOURCES" value="false" />
      <option name="ALIGN_MULTILINE_FOR" value="false" />
      <option name="INDENT_WHEN_CASES" value="false" />
      <option name="CALL_PARAMETERS_WRAP" value="1" />
      <option name="METHOD_PARAMETERS_WRAP" value="1" />
      <option name="RESOURCE_LIST_WRAP" value="5" />
      <option name="EXTENDS_LIST_WRAP" value="1" />
      <option name="THROWS_LIST_WRAP" value="1" />
      <option name="EXTENDS_KEYWORD_WRAP" value="1" />
      <option name="THROWS_KEYWORD_WRAP" value="1" />
      <option name="METHOD_CALL_CHAIN_WRAP" value="1" />
      <option name="BINARY_OPERATION_WRAP" value="1" />
      <option name="TERNARY_OPERATION_WRAP" value="1" />
      <option name="KEEP_SIMPLE_BLOCKS_IN_ONE_LINE" value="true" />
      <option name="KEEP_SIMPLE_METHODS_IN_ONE_LINE" value="true" />
      <option name="KEEP_SIMPLE_LAMBDAS_IN_ONE_LINE" value="true" />
      <option name="KEEP_SIMPLE_CLASSES_IN_ONE_LINE" value="true" />
      <option name="ARRAY_INITIALIZER_WRAP" value="1" />
      <option name="ARRAY_INITIALIZER_LBRACE_ON_NEXT_LINE" value="true" />
      <option name="ARRAY_INITIALIZER_RBRACE_ON_NEXT_LINE" value="true" />
      <option name="ASSIGNMENT_WRAP" value="1" />
      <option name="ASSERT_STATEMENT_WRAP" value="1" />
      <option name="IF_BRACE_FORCE" value="3" />
      <option name="DOWHILE_BRACE_FORCE" value="3" />
      <option name="WHILE_BRACE_FORCE" value="3" />
      <option name="FOR_BRACE_FORCE" value="3" />
      <option name="WRAP_LONG_LINES" value="true" />
      <indentOptions>
        <option name="INDENT_SIZE" value="2" />
        <option name="CONTINUATION_INDENT_SIZE" value="4" />
        <option name="TAB_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
  </code_scheme>
</component>
