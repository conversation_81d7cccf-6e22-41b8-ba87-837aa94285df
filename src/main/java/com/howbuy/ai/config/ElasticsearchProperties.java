package com.howbuy.ai.config;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Elasticsearch配置属性类
 *
 * <p>提供类型安全的配置属性绑定，支持IDE自动完成和验证
 *
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "spring.elasticsearch")
public class ElasticsearchProperties {

  /** Elasticsearch集群节点URI列表 示例：["http://localhost:9200", "http://localhost:9201"] */
  private List<String> uris = Arrays.asList("http://localhost:9200");

  /** 认证配置 */
  private Authentication authentication = new Authentication();

  /** 连接配置 */
  private Connection connection = new Connection();

  /** SSL配置 */
  private Ssl ssl = new Ssl();

  /** 连接池配置 */
  private Pool pool = new Pool();

  /** 认证配置类 */
  @Data
  public static class Authentication {
    /** 用户名 */
    private String username;

    /** 密码 */
    private String password;

    /** 是否启用认证 */
    public boolean isEnabled() {
      return username != null
          && !username.trim().isEmpty()
          && password != null
          && !password.trim().isEmpty();
    }
  }

  /** 连接配置类 */
  @Data
  public static class Connection {
    /** 连接超时时间 */
    private Duration connectTimeout = Duration.ofSeconds(10);

    /** Socket读取超时时间 */
    private Duration socketTimeout = Duration.ofSeconds(30);

    /** 连接保持活跃时间 */
    private Duration keepAlive = Duration.ofSeconds(30);

    /** 请求超时时间 */
    private Duration requestTimeout = Duration.ofMinutes(1);
  }

  /** SSL配置类 */
  @Data
  public static class Ssl {
    /** 是否启用SSL */
    private boolean enabled = false;

    /** 信任存储路径 */
    private String truststore;

    /** 信任存储密码 */
    private String truststorePassword;

    /** 密钥存储路径 */
    private String keystore;

    /** 密钥存储密码 */
    private String keystorePassword;

    /** 是否验证主机名 */
    private boolean verifyHostnames = true;
  }

  /** 连接池配置类 */
  @Data
  public static class Pool {
    /** 最大连接数 */
    private int maxConnections = 100;

    /** 每个路由的最大连接数 */
    private int maxConnectionsPerRoute = 10;

    /** 连接空闲超时时间 */
    private Duration idleTimeout = Duration.ofMinutes(5);

    /** 连接生存时间 */
    private Duration timeToLive = Duration.ofMinutes(30);
  }

  /** 获取第一个URI（用于兼容性） */
  public String getFirstUri() {
    return uris != null && !uris.isEmpty() ? uris.get(0) : "http://localhost:9200";
  }

  /** 检查是否配置了多个节点 */
  public boolean isClusterMode() {
    return uris != null && uris.size() > 1;
  }
}
