package com.howbuy.ai.config;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.log4j.Log4j2;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.elasticsearch.client.ClientConfiguration;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchConfiguration;
import org.springframework.data.elasticsearch.repository.config.EnableElasticsearchRepositories;
import org.springframework.util.StringUtils;

/**
 * Elasticsearch现代化配置类
 *
 * <p>主要特性： 1. 使用新的ElasticsearchClient替代废弃的RestHighLevelClient 2. 支持多节点集群配置 3. 完善的连接池和重试机制 4.
 * 支持SSL/TLS安全连接 5. 灵活的认证配置 6. 性能优化的连接参数
 *
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
@Log4j2
@Configuration
@EnableElasticsearchRepositories(basePackages = "com.howbuy.ai.repository")
public class ElasticsearchConfig extends ElasticsearchConfiguration {

  /** 注入Elasticsearch配置属性 */
  @Autowired private ElasticsearchProperties elasticsearchProperties;

  /**
   * 配置Elasticsearch客户端连接参数
   *
   * <p>这是新的推荐配置方式，替代了废弃的RestHighLevelClient
   *
   * @return ClientConfiguration 客户端配置对象
   */
  @Override
  public ClientConfiguration clientConfiguration() {
    log.info("正在配置Elasticsearch客户端连接...");

    // 解析URI列表，支持多节点集群
    List<String> hostList = parseElasticsearchHosts();

    // 创建客户端配置构建器
    ClientConfiguration.MaybeSecureClientConfigurationBuilder builder =
        ClientConfiguration.builder().connectedTo(hostList.toArray(new String[0]));

    // 配置连接超时
    builder.withConnectTimeout(elasticsearchProperties.getConnection().getConnectTimeout());

    // 配置Socket超时
    builder.withSocketTimeout(elasticsearchProperties.getConnection().getSocketTimeout());

    // 配置认证（如果提供了用户名和密码）
    if (elasticsearchProperties.getAuthentication().isEnabled()) {
      log.info(
          "配置Elasticsearch基本认证，用户名: {}", elasticsearchProperties.getAuthentication().getUsername());
      builder.withBasicAuth(
          elasticsearchProperties.getAuthentication().getUsername(),
          elasticsearchProperties.getAuthentication().getPassword());
    }

    // 配置SSL（如果启用）
    if (elasticsearchProperties.getSsl().isEnabled()) {
      log.info("启用Elasticsearch SSL连接");
      builder.usingSsl();
    }

    // 构建最终配置
    ClientConfiguration configuration = builder.build();

    log.info("Elasticsearch客户端配置完成，连接节点: {}", hostList);
    return configuration;
  }

  /**
   * 创建低级别的RestClient（用于高级操作）
   *
   * <p>提供更细粒度的连接控制，包括连接池配置
   *
   * @return RestClient 低级别客户端
   */
  @Bean
  @Primary
  public RestClient restClient() {
    log.info("创建Elasticsearch RestClient...");

    List<HttpHost> httpHosts = parseHttpHosts();

    RestClientBuilder builder = RestClient.builder(httpHosts.toArray(new HttpHost[0]));

    // 配置认证
    if (elasticsearchProperties.getAuthentication().isEnabled()) {
      CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
      credentialsProvider.setCredentials(
          AuthScope.ANY,
          new UsernamePasswordCredentials(
              elasticsearchProperties.getAuthentication().getUsername(),
              elasticsearchProperties.getAuthentication().getPassword()));

      builder.setHttpClientConfigCallback(
          httpClientBuilder ->
              httpClientBuilder
                  .setDefaultCredentialsProvider(credentialsProvider)
                  .setMaxConnTotal(elasticsearchProperties.getPool().getMaxConnections())
                  .setMaxConnPerRoute(
                      elasticsearchProperties.getPool().getMaxConnectionsPerRoute()));
    } else {
      builder.setHttpClientConfigCallback(
          httpClientBuilder ->
              httpClientBuilder
                  .setMaxConnTotal(elasticsearchProperties.getPool().getMaxConnections())
                  .setMaxConnPerRoute(
                      elasticsearchProperties.getPool().getMaxConnectionsPerRoute()));
    }

    // 配置请求超时
    builder.setRequestConfigCallback(
        requestConfigBuilder ->
            requestConfigBuilder
                .setConnectTimeout(
                    (int) elasticsearchProperties.getConnection().getConnectTimeout().toMillis())
                .setSocketTimeout(
                    (int) elasticsearchProperties.getConnection().getSocketTimeout().toMillis()));

    RestClient restClient = builder.build();
    log.info(
        "RestClient创建完成，最大连接数: {}, 每路由最大连接数: {}",
        elasticsearchProperties.getPool().getMaxConnections(),
        elasticsearchProperties.getPool().getMaxConnectionsPerRoute());

    return restClient;
  }

  /**
   * 创建新的ElasticsearchClient（推荐使用）
   *
   * <p>这是Elasticsearch官方推荐的新客户端，替代RestHighLevelClient
   *
   * @param restClient 低级别客户端
   * @return ElasticsearchClient 新的高级客户端
   */
  @Bean
  public ElasticsearchClient elasticsearchClient(RestClient restClient) {
    log.info("创建新的ElasticsearchClient...");

    ElasticsearchTransport transport =
        new RestClientTransport(restClient, new JacksonJsonpMapper());

    ElasticsearchClient client = new ElasticsearchClient(transport);
    log.info("ElasticsearchClient创建完成");

    return client;
  }

  /**
   * 解析Elasticsearch主机列表
   *
   * @return 主机地址列表
   */
  private List<String> parseElasticsearchHosts() {
    List<String> hostList = new ArrayList<>();

    List<String> uriList = elasticsearchProperties.getUris();
    if (uriList != null && !uriList.isEmpty()) {
      for (String uri : uriList) {
        String trimmedUri = uri.trim();
        if (StringUtils.hasText(trimmedUri)) {
          // 移除协议前缀，只保留host:port
          String host = trimmedUri.replaceAll("^https?://", "");
          hostList.add(host);
        }
      }
    }

    // 如果没有配置URI，使用默认值
    if (hostList.isEmpty()) {
      hostList.add("localhost:9200");
    }

    return hostList;
  }

  /**
   * 解析HTTP主机列表
   *
   * @return HttpHost列表
   */
  private List<HttpHost> parseHttpHosts() {
    List<HttpHost> httpHosts = new ArrayList<>();

    List<String> uriList = elasticsearchProperties.getUris();
    if (uriList != null && !uriList.isEmpty()) {
      for (String uri : uriList) {
        String trimmedUri = uri.trim();
        if (StringUtils.hasText(trimmedUri)) {
          try {
            URI parsedUri = URI.create(trimmedUri);
            String scheme = parsedUri.getScheme() != null ? parsedUri.getScheme() : "http";
            String host = parsedUri.getHost() != null ? parsedUri.getHost() : "localhost";
            int port = parsedUri.getPort() != -1 ? parsedUri.getPort() : 9200;

            httpHosts.add(new HttpHost(host, port, scheme));
          } catch (Exception e) {
            log.warn("无法解析Elasticsearch URI: {}, 错误: {}", trimmedUri, e.getMessage());
          }
        }
      }
    }

    // 如果没有配置URI或解析失败，使用默认值
    if (httpHosts.isEmpty()) {
      httpHosts.add(new HttpHost("localhost", 9200, "http"));
    }

    return httpHosts;
  }

  /**
   * Elasticsearch连接健康检查
   *
   * @param elasticsearchClient 客户端
   * @return 健康检查结果
   */
  @Bean
  public ElasticsearchHealthIndicator elasticsearchHealthIndicator(
      ElasticsearchClient elasticsearchClient) {
    return new ElasticsearchHealthIndicator(elasticsearchClient);
  }

  /** 自定义健康检查指示器 */
  public static class ElasticsearchHealthIndicator {
    private final ElasticsearchClient client;

    public ElasticsearchHealthIndicator(ElasticsearchClient client) {
      this.client = client;
    }

    /**
     * 检查Elasticsearch连接状态
     *
     * @return 是否连接正常
     */
    public boolean isHealthy() {
      try {
        // 执行简单的ping操作
        return client.ping().value();
      } catch (Exception e) {
        log.error("Elasticsearch健康检查失败", e);
        return false;
      }
    }
  }
}
