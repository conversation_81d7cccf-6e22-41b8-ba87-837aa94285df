package com.howbuy.ai.config;

import com.howbuy.ai.entity.WordDocument;
import com.howbuy.ai.service.WordSearchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 数据初始化器
 * 应用启动时初始化一些示例数据
 */
@Component
public class DataInitializer implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(DataInitializer.class);
    
    @Autowired
    private WordSearchService wordSearchService;
    
    @Override
    public void run(String... args) throws Exception {
        logger.info("开始初始化示例数据...");
        
        try {
            // 创建示例文档
            createSampleDocuments();
            logger.info("示例数据初始化完成");
        } catch (Exception e) {
            logger.warn("示例数据初始化失败，可能是Elasticsearch未启动: {}", e.getMessage());
        }
    }
    
    private void createSampleDocuments() {
        // 示例文档1
        WordDocument doc1 = new WordDocument();
        doc1.setTitle("Spring Boot入门指南");
        doc1.setContent("Spring Boot是一个基于Spring框架的快速开发工具，它简化了Spring应用的配置和部署。" +
                "通过自动配置和起步依赖，开发者可以快速创建独立的、生产级别的Spring应用程序。");
        doc1.setCategory("技术文档");
        doc1.setAuthor("张三");
        doc1.setTags(new String[]{"Spring", "Java", "框架", "入门"});
        
        // 示例文档2
        WordDocument doc2 = new WordDocument();
        doc2.setTitle("Elasticsearch搜索引擎详解");
        doc2.setContent("Elasticsearch是一个分布式、RESTful风格的搜索和数据分析引擎。" +
                "它基于Apache Lucene构建，提供了强大的全文搜索功能和实时数据分析能力。" +
                "广泛应用于日志分析、搜索引擎、数据可视化等场景。");
        doc2.setCategory("技术文档");
        doc2.setAuthor("李四");
        doc2.setTags(new String[]{"Elasticsearch", "搜索", "大数据", "分析"});
        
        // 示例文档3
        WordDocument doc3 = new WordDocument();
        doc3.setTitle("Java并发编程最佳实践");
        doc3.setContent("Java并发编程是Java开发中的重要主题。本文介绍了线程安全、锁机制、" +
                "线程池、并发集合等核心概念，以及在实际项目中的最佳实践。" +
                "掌握并发编程对于开发高性能的Java应用程序至关重要。");
        doc3.setCategory("编程指南");
        doc3.setAuthor("王五");
        doc3.setTags(new String[]{"Java", "并发", "多线程", "性能优化"});
        
        // 示例文档4
        WordDocument doc4 = new WordDocument();
        doc4.setTitle("微服务架构设计原则");
        doc4.setContent("微服务架构是一种将单一应用程序分解为多个小型、独立服务的架构模式。" +
                "每个服务运行在自己的进程中，通过轻量级的通信机制进行交互。" +
                "本文探讨了微服务的设计原则、优势和挑战。");
        doc4.setCategory("架构设计");
        doc4.setAuthor("赵六");
        doc4.setTags(new String[]{"微服务", "架构", "分布式", "设计模式"});
        
        // 保存示例文档
        try {
            wordSearchService.saveDocument(doc1);
            wordSearchService.saveDocument(doc2);
            wordSearchService.saveDocument(doc3);
            wordSearchService.saveDocument(doc4);
            logger.info("成功创建4个示例文档");
        } catch (Exception e) {
            logger.error("保存示例文档失败", e);
        }
    }
}
