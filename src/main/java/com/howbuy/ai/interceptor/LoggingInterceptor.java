package com.howbuy.ai.interceptor;

import com.howbuy.ai.util.PerformanceLogger;
import java.util.UUID;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/** 日志拦截器 自动记录API请求的性能和访问日志 */
@Component
public class LoggingInterceptor implements HandlerInterceptor {

  private static final Logger logger = LogManager.getLogger(LoggingInterceptor.class);
  private static final String REQUEST_START_TIME = "requestStartTime";
  private static final String REQUEST_ID = "requestId";

  @Override
  public boolean preHandle(
      HttpServletRequest request, HttpServletResponse response, Object handler) {
    // 生成请求ID
    String requestId = UUID.randomUUID().toString().substring(0, 8);
    request.setAttribute(REQUEST_ID, requestId);

    // 记录请求开始时间
    long startTime = System.currentTimeMillis();
    request.setAttribute(REQUEST_START_TIME, startTime);

    // 记录请求信息
    String method = request.getMethod();
    String uri = request.getRequestURI();
    String queryString = request.getQueryString();
    String clientIp = getClientIpAddress(request);
    String userAgent = request.getHeader("User-Agent");

    logger.info(
        "API请求开始 - ID: {}, 方法: {} {}, 客户端IP: {}, User-Agent: {}",
        requestId,
        method,
        uri + (queryString != null ? "?" + queryString : ""),
        clientIp,
        userAgent);

    return true;
  }

  @Override
  public void afterCompletion(
      HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
    // 获取请求信息
    String requestId = (String) request.getAttribute(REQUEST_ID);
    Long startTime = (Long) request.getAttribute(REQUEST_START_TIME);

    if (startTime != null) {
      long executionTime = System.currentTimeMillis() - startTime;
      String method = request.getMethod();
      String uri = request.getRequestURI();
      int statusCode = response.getStatus();

      // 记录API性能
      PerformanceLogger.logApiPerformance(uri, method, statusCode, executionTime);

      // 记录请求完成信息
      if (ex != null) {
        logger.error(
            "API请求异常 - ID: {}, 方法: {} {}, 状态码: {}, 执行时间: {}ms, 异常: {}",
            requestId,
            method,
            uri,
            statusCode,
            executionTime,
            ex.getMessage());
      } else {
        logger.info(
            "API请求完成 - ID: {}, 方法: {} {}, 状态码: {}, 执行时间: {}ms",
            requestId,
            method,
            uri,
            statusCode,
            executionTime);
      }
    }
  }

  /** 获取客户端真实IP地址 */
  private String getClientIpAddress(HttpServletRequest request) {
    String xForwardedFor = request.getHeader("X-Forwarded-For");
    if (xForwardedFor != null
        && !xForwardedFor.isEmpty()
        && !"unknown".equalsIgnoreCase(xForwardedFor)) {
      return xForwardedFor.split(",")[0].trim();
    }

    String xRealIp = request.getHeader("X-Real-IP");
    if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
      return xRealIp;
    }

    return request.getRemoteAddr();
  }
}
