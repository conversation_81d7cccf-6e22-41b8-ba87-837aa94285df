package com.howbuy.ai.util;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

/** 性能日志工具类 用于记录方法执行时间和性能相关信息 */
@Component
public class PerformanceLogger {

  private static final Logger perfLogger = LogManager.getLogger("com.howbuy.ai.performance");
  private static final Logger logger = LogManager.getLogger(PerformanceLogger.class);

  /**
   * 记录方法执行时间
   *
   * @param methodName 方法名
   * @param executionTime 执行时间（毫秒）
   * @param params 方法参数
   */
  public static void logExecutionTime(String methodName, long executionTime, Object... params) {
    if (executionTime > 1000) { // 超过1秒的操作记录为慢操作
      perfLogger.warn(
          "慢操作检测 - 方法: {}, 执行时间: {}ms, 参数: {}", methodName, executionTime, formatParams(params));
    } else if (executionTime > 500) { // 超过500ms的操作记录为警告
      perfLogger.info(
          "性能警告 - 方法: {}, 执行时间: {}ms, 参数: {}", methodName, executionTime, formatParams(params));
    } else {
      perfLogger.debug(
          "方法执行 - 方法: {}, 执行时间: {}ms, 参数: {}", methodName, executionTime, formatParams(params));
    }
  }

  /**
   * 记录搜索性能
   *
   * @param searchType 搜索类型
   * @param keyword 搜索关键词
   * @param resultCount 结果数量
   * @param executionTime 执行时间
   */
  public static void logSearchPerformance(
      String searchType, String keyword, int resultCount, long executionTime) {
    perfLogger.info(
        "搜索性能 - 类型: {}, 关键词: {}, 结果数: {}, 执行时间: {}ms",
        searchType,
        keyword,
        resultCount,
        executionTime);
  }

  /**
   * 记录Elasticsearch操作性能
   *
   * @param operation 操作类型
   * @param indexName 索引名称
   * @param documentId 文档ID
   * @param executionTime 执行时间
   */
  public static void logElasticsearchPerformance(
      String operation, String indexName, String documentId, long executionTime) {
    perfLogger.info(
        "ES操作性能 - 操作: {}, 索引: {}, 文档ID: {}, 执行时间: {}ms",
        operation,
        indexName,
        documentId,
        executionTime);
  }

  /**
   * 记录API请求性能
   *
   * @param endpoint API端点
   * @param method HTTP方法
   * @param statusCode 响应状态码
   * @param executionTime 执行时间
   */
  public static void logApiPerformance(
      String endpoint, String method, int statusCode, long executionTime) {
    if (statusCode >= 400) {
      perfLogger.warn(
          "API错误 - 端点: {} {}, 状态码: {}, 执行时间: {}ms", method, endpoint, statusCode, executionTime);
    } else if (executionTime > 2000) {
      perfLogger.warn(
          "API慢响应 - 端点: {} {}, 状态码: {}, 执行时间: {}ms", method, endpoint, statusCode, executionTime);
    } else {
      perfLogger.info(
          "API请求 - 端点: {} {}, 状态码: {}, 执行时间: {}ms", method, endpoint, statusCode, executionTime);
    }
  }

  /** 格式化参数 */
  private static String formatParams(Object... params) {
    if (params == null || params.length == 0) {
      return "无参数";
    }

    StringBuilder sb = new StringBuilder();
    for (int i = 0; i < params.length; i++) {
      if (i > 0) {
        sb.append(", ");
      }
      sb.append(params[i]);
    }
    return sb.toString();
  }

  /** 性能监控装饰器 用于包装需要监控性能的方法 */
  public static class PerformanceMonitor {
    private final String methodName;
    private final long startTime;
    private Object[] params;

    public PerformanceMonitor(String methodName, Object... params) {
      this.methodName = methodName;
      this.params = params;
      this.startTime = System.currentTimeMillis();
      logger.debug("开始执行方法: {}, 参数: {}", methodName, formatParams(params));
    }

    public void end() {
      long executionTime = System.currentTimeMillis() - startTime;
      logExecutionTime(methodName, executionTime, params);
    }

    public void endWithResult(Object result) {
      long executionTime = System.currentTimeMillis() - startTime;
      logger.debug("方法执行完成: {}, 结果: {}, 执行时间: {}ms", methodName, result, executionTime);
      logExecutionTime(methodName, executionTime, params);
    }
  }
}
