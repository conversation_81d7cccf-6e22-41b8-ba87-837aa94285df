package com.howbuy.ai.util;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.log4j.Log4j2;
import org.springframework.util.StringUtils;

/** 数字转换工具类 提供阿拉伯数字转中文数字的功能 */
@Log4j2
public final class NumberConversionUtil {

  /** 数字匹配正则表达式（线程安全的静态Pattern） */
  private static final Pattern NUMBER_PATTERN = Pattern.compile("\\d+");

  /** 中文数字字符 */
  private static final String[] CHINESE_NUMBERS = {
    "零", "一", "二", "三", "四", "五", "六", "七", "八", "九"
  };

  /** 中文数字单位 */
  private static final String[] CHINESE_UNITS = {
    "", "十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千"
  };

  /** 支持的最大数字（避免溢出） */
  private static final int MAX_SUPPORTED_NUMBER = 999999999; // 9亿以内

  /** 私有构造函数，防止实例化 */
  private NumberConversionUtil() {
    throw new UnsupportedOperationException("Utility class cannot be instantiated");
  }

  /**
   * 将字符串中的阿拉伯数字替换为中文数字
   *
   * @param source 源字符串
   * @return 替换后的字符串
   */
  public static String replaceNumbersWithChinese(String source) {
    if (!StringUtils.hasText(source)) {
      log.debug("输入字符串为空，返回原字符串");
      return source;
    }

    try {
      StringBuilder result = new StringBuilder();
      Matcher matcher = NUMBER_PATTERN.matcher(source);
      int lastEnd = 0;

      // 遍历所有匹配的数字
      while (matcher.find()) {
        // 添加数字前的文本
        result.append(source, lastEnd, matcher.start());

        String numberString = matcher.group();

        try {
          long number = Long.parseLong(numberString);

          // 检查数字范围
          if (number > MAX_SUPPORTED_NUMBER) {
            log.warn("数字 {} 超出支持范围，保持原样", number);
            result.append(numberString); // 保持原数字
          } else {
            String chineseNumber = convertIntegerToChinese((int) number);
            if (chineseNumber != null) {
              result.append(chineseNumber);
              log.debug("数字转换: {} -> {}", numberString, chineseNumber);
            } else {
              result.append(numberString); // 转换失败，保持原数字
            }
          }
        } catch (NumberFormatException e) {
          log.warn("解析数字失败: {}, 保持原样", numberString);
          result.append(numberString); // 解析失败，保持原数字
        }

        lastEnd = matcher.end();
      }

      // 添加最后一个数字后的文本
      result.append(source, lastEnd, source.length());

      String finalResult = result.toString();
      log.debug("数字转换完成: {} -> {}", source, finalResult);
      return finalResult;

    } catch (Exception e) {
      log.error("数字转换过程中发生错误: {}", source, e);
      return source; // 发生错误时返回原字符串
    }
  }

  /**
   * 将整数转换为中文数字
   *
   * @param number 要转换的整数
   * @return 中文数字字符串，转换失败时返回null
   */
  public static String convertIntegerToChinese(int number) {
    if (number < 0) {
      log.warn("不支持负数转换: {}", number);
      return null;
    }

    if (number == 0) {
      return CHINESE_NUMBERS[0]; // "零"
    }

    if (number > MAX_SUPPORTED_NUMBER) {
      log.warn("数字 {} 超出支持范围", number);
      return null;
    }

    try {
      StringBuilder result = new StringBuilder();
      int unitIndex = 0;
      int tempNumber = number;

      // 从个位开始处理
      while (tempNumber > 0) {
        int digit = tempNumber % 10;
        String digitChinese = CHINESE_NUMBERS[digit];
        String unit = (unitIndex < CHINESE_UNITS.length) ? CHINESE_UNITS[unitIndex] : "";

        result.insert(0, digitChinese + unit);
        tempNumber = tempNumber / 10;
        unitIndex++;
      }

      // 处理中文数字的规则
      String chineseNumber =
          result
              .toString()
              .replaceAll("零[千百十]", "零") // 零千、零百、零十 -> 零
              .replaceAll("零+万", "万") // 零零万 -> 万
              .replaceAll("零+亿", "亿") // 零零亿 -> 亿
              .replaceAll("亿万", "亿零") // 亿万 -> 亿零
              .replaceAll("零+", "零") // 多个零 -> 一个零
              .replaceAll("^一十", "十") // 一十 -> 十（开头）
              .replaceAll("零$", ""); // 末尾的零

      log.debug("数字转换: {} -> {}", number, chineseNumber);
      return chineseNumber;

    } catch (Exception e) {
      log.error("转换数字 {} 时发生错误", number, e);
      return null;
    }
  }

  /**
   * 检查字符串是否包含数字
   *
   * @param text 要检查的字符串
   * @return 如果包含数字返回true，否则返回false
   */
  public static boolean containsNumbers(String text) {
    if (!StringUtils.hasText(text)) {
      return false;
    }
    return NUMBER_PATTERN.matcher(text).find();
  }

  /**
   * 获取字符串中所有的数字
   *
   * @param text 要处理的字符串
   * @return 数字数组
   */
  public static int[] extractNumbers(String text) {
    if (!StringUtils.hasText(text)) {
      return new int[0];
    }

    Matcher matcher = NUMBER_PATTERN.matcher(text);
    List<Integer> numbers = new ArrayList<>();

    while (matcher.find()) {
      try {
        long number = Long.parseLong(matcher.group());
        if (number <= Integer.MAX_VALUE) {
          numbers.add((int) number);
        }
      } catch (NumberFormatException e) {
        log.warn("解析数字失败: {}", matcher.group());
      }
    }

    return numbers.stream().mapToInt(Integer::intValue).toArray();
  }
}
