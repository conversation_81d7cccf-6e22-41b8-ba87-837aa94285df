package com.howbuy.ai.dto;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * 搜索请求DTO
 * 用于接收前端搜索参数
 */
public class SearchRequest {
    
    @NotBlank(message = "搜索关键词不能为空")
    private String keyword;
    
    private String title;
    
    private String content;
    
    private String category;
    
    private String author;
    
    private String tag;
    
    @Min(value = 0, message = "页码不能小于0")
    private int page = 0;
    
    @Min(value = 1, message = "每页大小不能小于1")
    private int size = 10;
    
    // 构造函数
    public SearchRequest() {}
    
    public SearchRequest(String keyword) {
        this.keyword = keyword;
    }
    
    // Getter和Setter方法
    public String getKeyword() {
        return keyword;
    }
    
    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public String getAuthor() {
        return author;
    }
    
    public void setAuthor(String author) {
        this.author = author;
    }
    
    public String getTag() {
        return tag;
    }
    
    public void setTag(String tag) {
        this.tag = tag;
    }
    
    public int getPage() {
        return page;
    }
    
    public void setPage(int page) {
        this.page = page;
    }
    
    public int getSize() {
        return size;
    }
    
    public void setSize(int size) {
        this.size = size;
    }
    
    @Override
    public String toString() {
        return "SearchRequest{" +
                "keyword='" + keyword + '\'' +
                ", title='" + title + '\'' +
                ", content='" + content + '\'' +
                ", category='" + category + '\'' +
                ", author='" + author + '\'' +
                ", tag='" + tag + '\'' +
                ", page=" + page +
                ", size=" + size +
                '}';
    }
}
