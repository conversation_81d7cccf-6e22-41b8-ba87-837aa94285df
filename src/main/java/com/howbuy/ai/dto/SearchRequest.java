package com.howbuy.ai.dto;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import lombok.*;

/** 搜索请求DTO 用于接收前端搜索参数 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SearchRequest {

  @NotBlank(message = "搜索关键词不能为空")
  private String keyword;

  private String title;

  private String content;

  private String category;

  private String author;

  private String tag;

  @Min(value = 0, message = "页码不能小于0")
  @Builder.Default
  private int page = 0;

  @Min(value = 1, message = "每页大小不能小于1")
  @Builder.Default
  private int size = 10;

  /** 便捷构造函数，只设置关键词 */
  public SearchRequest(String keyword) {
    this.keyword = keyword;
    this.page = 0;
    this.size = 10;
  }
}
