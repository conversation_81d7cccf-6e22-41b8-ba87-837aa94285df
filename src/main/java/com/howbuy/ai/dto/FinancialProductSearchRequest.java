package com.howbuy.ai.dto;

import lombok.*;

import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.util.Collections;
import java.util.List;

/**
 * 金融产品搜索请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FinancialProductSearchRequest {

    /**
     * 搜索关键词（支持产品名称、代码、拼音搜索）
     */
    @Size(max = 100, message = "搜索关键词长度不能超过100个字符")
    private String keyword;

    /**
     * 产品代码
     */
    @Size(max = 20, message = "产品代码长度不能超过20个字符")
    private String tscode;

    /**
     * 产品名称
     */
    @Size(max = 100, message = "产品名称长度不能超过100个字符")
    private String tsname;

    /**
     * 产品类型列表
     */
    private List<String> tsTypes;

    /**
     * 业务类型列表
     */
    private List<String> businessTypes;

    /**
     * 产品属性列表
     */
    private List<String> attributes;

    /**
     * 申购状态 (null-不限制, 0-不可申购, 1-可申购)
     */
    private Long opBuyStatus;

    /**
     * 赎回状态 (null-不限制, 0-不可赎回, 1-可赎回)
     */
    private Long opRedeemStatus;

    /**
     * 销售标志 (null-不限制, 0-不在售, 1-在售)
     */
    private Long xsbz;

    /**
     * 展示标志 (null-不限制, 0-不展示, 1-展示)
     */
    private Long zsbz;

    /**
     * 最小权重
     */
    @Min(value = 0, message = "最小权重不能小于0")
    private Long minWeight;

    /**
     * 最大权重
     */
    @Min(value = 0, message = "最大权重不能小于0")
    private Long maxWeight;

    /**
     * 是否启用拼音搜索
     */
    @Builder.Default
    private Boolean enablePinyinSearch = true;

    /**
     * 是否启用模糊搜索
     */
    @Builder.Default
    private Boolean enableFuzzySearch = false;

    /**
     * 搜索类型
     */
    @Builder.Default
    private SearchType searchType = SearchType.COMPREHENSIVE;

    /**
     * 排序字段
     */
    @Builder.Default
    private SortField sortField = SortField.WEIGHT;

    /**
     * 排序方向
     */
    @Builder.Default
    private SortDirection sortDirection = SortDirection.DESC;

    /**
     * 页码（从0开始）
     */
    @Min(value = 0, message = "页码不能小于0")
    @Builder.Default
    private int page = 0;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小不能小于1")
    @Builder.Default
    private int size = 20;

    /**
     * 搜索类型枚举
     */
    public enum SearchType {
        /**
         * 综合搜索（名称+代码+拼音）
         */
        COMPREHENSIVE,
        /**
         * 精确匹配
         */
        EXACT,
        /**
         * 前缀匹配
         */
        PREFIX,
        /**
         * 拼音搜索
         */
        PINYIN,
        /**
         * 模糊搜索
         */
        FUZZY
    }

    /**
     * 排序字段枚举
     */
    public enum SortField {
        /**
         * 按权重排序
         */
        WEIGHT("weight"),
        /**
         * 按产品代码排序
         */
        TSCODE("tscode.keyword"),
        /**
         * 按产品名称排序
         */
        TSNAME("tsname"),
        /**
         * 按相关性排序
         */
        SCORE("_score");

        private final String fieldName;

        SortField(String fieldName) {
            this.fieldName = fieldName;
        }

        public String getFieldName() {
            return fieldName;
        }
    }

    /**
     * 排序方向枚举
     */
    public enum SortDirection {
        /**
         * 升序
         */
        ASC,
        /**
         * 降序
         */
        DESC
    }

    /**
     * 便捷构造函数 - 关键词搜索
     */
    public FinancialProductSearchRequest(String keyword) {
        this.keyword = keyword;
        this.enablePinyinSearch = true;
        this.searchType = SearchType.COMPREHENSIVE;
        this.sortField = SortField.WEIGHT;
        this.sortDirection = SortDirection.DESC;
        this.page = 0;
        this.size = 20;
    }

    /**
     * 便捷构造函数 - 产品代码搜索
     */
    public static FinancialProductSearchRequest byTscode(String tscode) {
        return FinancialProductSearchRequest.builder()
                .tscode(tscode)
                .searchType(SearchType.EXACT)
                .build();
    }

    /**
     * 便捷构造函数 - 产品类型搜索
     */
    public static FinancialProductSearchRequest byTsType(String tsType) {
        return FinancialProductSearchRequest.builder()
                .tsTypes(Collections.singletonList(tsType))
                .build();
    }

    /**
     * 便捷构造函数 - 可申购产品搜索
     */
    public static FinancialProductSearchRequest buyableProducts() {
        return FinancialProductSearchRequest.builder()
                .opBuyStatus(1L)
                .xsbz(1L)
                .zsbz(1L)
                .build();
    }
}
