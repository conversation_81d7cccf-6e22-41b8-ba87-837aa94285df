package com.howbuy.ai.controller;

import com.howbuy.ai.dto.ApiResponse;
import com.howbuy.ai.dto.FinancialProductSearchRequest;
import com.howbuy.ai.entity.FinancialProduct;
import com.howbuy.ai.service.FinancialProductService;
import java.util.Optional;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/** 金融产品搜索REST控制器 */
@Log4j2
@RestController
@RequestMapping("/api/v1/financial-products")
@Validated
public class FinancialProductController {

  @Autowired private FinancialProductService financialProductService;

  /** 根据产品代码获取产品详情 */
  @GetMapping("/{tscode}")
  public ResponseEntity<ApiResponse<FinancialProduct>> getProductByTscode(
      @PathVariable @NotBlank(message = "产品代码不能为空") String tscode) {

    log.info("获取产品详情 - 代码: {}", tscode);

    try {
      Optional<FinancialProduct> product = financialProductService.findByTscode(tscode);

      if (product.isPresent()) {
        return ResponseEntity.ok(ApiResponse.success("获取成功", product.get()));
      } else {
        return ResponseEntity.notFound().build();
      }
    } catch (Exception e) {
      log.error("获取产品详情失败", e);
      return ResponseEntity.internalServerError()
          .body(ApiResponse.error("获取产品详情失败: " + e.getMessage()));
    }
  }

  /** 综合搜索金融产品 */
  @PostMapping("/search")
  public ResponseEntity<ApiResponse<Page<FinancialProduct>>> search(
      @Valid @RequestBody FinancialProductSearchRequest request) {

    log.info("金融产品搜索请求: {}", request);

    try {
      Page<FinancialProduct> results = financialProductService.search(request);
      return ResponseEntity.ok(ApiResponse.success("搜索成功", results));
    } catch (Exception e) {
      log.error("金融产品搜索失败", e);
      return ResponseEntity.internalServerError()
          .body(ApiResponse.error("搜索失败: " + e.getMessage()));
    }
  }

  /** 简单关键词搜索 */
  @GetMapping("/search")
  public ResponseEntity<ApiResponse<Page<FinancialProduct>>> simpleSearch(
      @RequestParam @NotBlank(message = "搜索关键词不能为空") String keyword,
      @RequestParam(defaultValue = "COMPREHENSIVE")
          FinancialProductSearchRequest.SearchType searchType,
      @RequestParam(defaultValue = "0") @Min(0) int page,
      @RequestParam(defaultValue = "20") @Min(1) int size) {

    log.info("简单搜索 - 关键词: {}, 类型: {}, 页码: {}, 大小: {}", keyword, searchType, page, size);

    try {
      FinancialProductSearchRequest request =
          FinancialProductSearchRequest.builder()
              .keyword(keyword)
              .searchType(searchType)
              .page(page)
              .size(size)
              .build();

      Page<FinancialProduct> results = financialProductService.search(request);
      return ResponseEntity.ok(ApiResponse.success("搜索成功", results));
    } catch (Exception e) {
      log.error("简单搜索失败", e);
      return ResponseEntity.internalServerError()
          .body(ApiResponse.error("搜索失败: " + e.getMessage()));
    }
  }

  /** 获取可申购产品 */
  @GetMapping("/buyable")
  public ResponseEntity<ApiResponse<Page<FinancialProduct>>> getBuyableProducts(
      @RequestParam(defaultValue = "0") @Min(0) int page,
      @RequestParam(defaultValue = "20") @Min(1) int size) {

    log.info("获取可申购产品 - 页码: {}, 大小: {}", page, size);

    try {
      Page<FinancialProduct> results = financialProductService.getBuyableProducts(page, size);
      return ResponseEntity.ok(ApiResponse.success("获取成功", results));
    } catch (Exception e) {
      log.error("获取可申购产品失败", e);
      return ResponseEntity.internalServerError()
          .body(ApiResponse.error("获取可申购产品失败: " + e.getMessage()));
    }
  }

  /** 获取可赎回产品 */
  @GetMapping("/redeemable")
  public ResponseEntity<ApiResponse<Page<FinancialProduct>>> getRedeemableProducts(
      @RequestParam(defaultValue = "0") @Min(0) int page,
      @RequestParam(defaultValue = "20") @Min(1) int size) {

    log.info("获取可赎回产品 - 页码: {}, 大小: {}", page, size);

    try {
      Page<FinancialProduct> results = financialProductService.getRedeemableProducts(page, size);
      return ResponseEntity.ok(ApiResponse.success("获取成功", results));
    } catch (Exception e) {
      log.error("获取可赎回产品失败", e);
      return ResponseEntity.internalServerError()
          .body(ApiResponse.error("获取可赎回产品失败: " + e.getMessage()));
    }
  }

  /** 获取热门产品 */
  @GetMapping("/popular")
  public ResponseEntity<ApiResponse<Page<FinancialProduct>>> getPopularProducts(
      @RequestParam(defaultValue = "0") @Min(0) int page,
      @RequestParam(defaultValue = "20") @Min(1) int size) {

    log.info("获取热门产品 - 页码: {}, 大小: {}", page, size);

    try {
      Page<FinancialProduct> results = financialProductService.getPopularProducts(page, size);
      return ResponseEntity.ok(ApiResponse.success("获取成功", results));
    } catch (Exception e) {
      log.error("获取热门产品失败", e);
      return ResponseEntity.internalServerError()
          .body(ApiResponse.error("获取热门产品失败: " + e.getMessage()));
    }
  }

  /** 根据产品类型获取产品 */
  @GetMapping("/type/{tsType}")
  public ResponseEntity<ApiResponse<Page<FinancialProduct>>> getProductsByType(
      @PathVariable @NotBlank(message = "产品类型不能为空") String tsType,
      @RequestParam(defaultValue = "0") @Min(0) int page,
      @RequestParam(defaultValue = "20") @Min(1) int size) {

    log.info("根据产品类型获取产品 - 类型: {}, 页码: {}, 大小: {}", tsType, page, size);

    try {
      Page<FinancialProduct> results =
          financialProductService.getProductsByType(tsType, page, size);
      return ResponseEntity.ok(ApiResponse.success("获取成功", results));
    } catch (Exception e) {
      log.error("根据产品类型获取产品失败", e);
      return ResponseEntity.internalServerError()
          .body(ApiResponse.error("获取产品失败: " + e.getMessage()));
    }
  }

  /** 拼音搜索 */
  @GetMapping("/search/pinyin")
  public ResponseEntity<ApiResponse<Page<FinancialProduct>>> pinyinSearch(
      @RequestParam @NotBlank(message = "搜索关键词不能为空") String keyword,
      @RequestParam(defaultValue = "0") @Min(0) int page,
      @RequestParam(defaultValue = "20") @Min(1) int size) {

    log.info("拼音搜索 - 关键词: {}, 页码: {}, 大小: {}", keyword, page, size);

    try {
      FinancialProductSearchRequest request =
          FinancialProductSearchRequest.builder()
              .keyword(keyword)
              .searchType(FinancialProductSearchRequest.SearchType.PINYIN)
              .page(page)
              .size(size)
              .build();

      Page<FinancialProduct> results = financialProductService.search(request);
      return ResponseEntity.ok(ApiResponse.success("拼音搜索成功", results));
    } catch (Exception e) {
      log.error("拼音搜索失败", e);
      return ResponseEntity.internalServerError()
          .body(ApiResponse.error("拼音搜索失败: " + e.getMessage()));
    }
  }

  /** 前缀搜索（主要用于产品代码搜索） */
  @GetMapping("/search/prefix")
  public ResponseEntity<ApiResponse<Page<FinancialProduct>>> prefixSearch(
      @RequestParam @NotBlank(message = "搜索前缀不能为空") String prefix,
      @RequestParam(defaultValue = "0") @Min(0) int page,
      @RequestParam(defaultValue = "20") @Min(1) int size) {

    log.info("前缀搜索 - 前缀: {}, 页码: {}, 大小: {}", prefix, page, size);

    try {
      FinancialProductSearchRequest request =
          FinancialProductSearchRequest.builder()
              .keyword(prefix)
              .searchType(FinancialProductSearchRequest.SearchType.PREFIX)
              .page(page)
              .size(size)
              .build();

      Page<FinancialProduct> results = financialProductService.search(request);
      return ResponseEntity.ok(ApiResponse.success("前缀搜索成功", results));
    } catch (Exception e) {
      log.error("前缀搜索失败", e);
      return ResponseEntity.internalServerError()
          .body(ApiResponse.error("前缀搜索失败: " + e.getMessage()));
    }
  }
}
