package com.howbuy.ai.controller;

import com.howbuy.ai.dto.ApiResponse;
import com.howbuy.ai.entity.WordDocument;
import com.howbuy.ai.service.WordSearchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Optional;

/**
 * 文档管理REST控制器
 * 提供文档的增删改查操作
 */
@RestController
@RequestMapping("/v1/documents")
@Validated
public class DocumentController {
    
    private static final Logger logger = LoggerFactory.getLogger(DocumentController.class);
    
    @Autowired
    private WordSearchService wordSearchService;
    
    /**
     * 创建新文档
     * POST /api/v1/documents
     */
    @PostMapping
    public ResponseEntity<ApiResponse<WordDocument>> createDocument(
            @Valid @RequestBody WordDocument document) {
        
        logger.info("创建文档请求: {}", document.getTitle());
        
        try {
            WordDocument savedDocument = wordSearchService.saveDocument(document);
            return ResponseEntity.ok(ApiResponse.success("文档创建成功", savedDocument));
        } catch (Exception e) {
            logger.error("创建文档失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("创建文档失败: " + e.getMessage()));
        }
    }
    
    /**
     * 更新文档
     * PUT /api/v1/documents/{id}
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<WordDocument>> updateDocument(
            @PathVariable String id,
            @Valid @RequestBody WordDocument document) {
        
        logger.info("更新文档请求 - ID: {}", id);
        
        try {
            Optional<WordDocument> existingDoc = wordSearchService.findById(id);
            if (existingDoc.isPresent()) {
                document.setId(id);
                WordDocument updatedDocument = wordSearchService.saveDocument(document);
                return ResponseEntity.ok(ApiResponse.success("文档更新成功", updatedDocument));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("更新文档失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("更新文档失败: " + e.getMessage()));
        }
    }
    
    /**
     * 删除文档
     * DELETE /api/v1/documents/{id}
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteDocument(@PathVariable String id) {
        
        logger.info("删除文档请求 - ID: {}", id);
        
        try {
            Optional<WordDocument> existingDoc = wordSearchService.findById(id);
            if (existingDoc.isPresent()) {
                wordSearchService.deleteDocument(id);
                return ResponseEntity.ok(ApiResponse.success("文档删除成功", null));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("删除文档失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("删除文档失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取文档详情
     * GET /api/v1/documents/{id}
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<WordDocument>> getDocument(@PathVariable String id) {
        
        logger.info("获取文档详情 - ID: {}", id);
        
        try {
            Optional<WordDocument> document = wordSearchService.findById(id);
            if (document.isPresent()) {
                return ResponseEntity.ok(ApiResponse.success("获取成功", document.get()));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("获取文档详情失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取失败: " + e.getMessage()));
        }
    }
}
