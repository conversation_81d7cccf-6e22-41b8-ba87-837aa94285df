package com.howbuy.ai.controller;

import com.howbuy.ai.dto.ApiResponse;
import com.howbuy.ai.dto.SearchRequest;
import com.howbuy.ai.entity.WordDocument;
import com.howbuy.ai.service.WordSearchService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Optional;

/**
 * 词汇搜索REST控制器
 * 提供文档搜索和管理的RESTful API
 */
@RestController
@RequestMapping("/v1/search")
@Validated
public class WordSearchController {
    
    private static final Logger logger = LogManager.getLogger(WordSearchController.class);
    
    @Autowired
    private WordSearchService wordSearchService;
    
    /**
     * 关键词搜索
     * GET /api/v1/search?keyword=xxx&page=0&size=10
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Page<WordDocument>>> search(
            @RequestParam @NotBlank(message = "搜索关键词不能为空") String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        logger.info("搜索请求 - 关键词: {}, 页码: {}, 大小: {}", keyword, page, size);
        
        try {
            Page<WordDocument> results = wordSearchService.searchByKeyword(keyword, page, size);
            return ResponseEntity.ok(ApiResponse.success("搜索成功", results));
        } catch (Exception e) {
            logger.error("搜索失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("搜索失败: " + e.getMessage()));
        }
    }
    
    /**
     * 高级搜索
     * POST /api/v1/search/advanced
     */
    @PostMapping("/advanced")
    public ResponseEntity<ApiResponse<Page<WordDocument>>> advancedSearch(
            @Valid @RequestBody SearchRequest request) {
        
        logger.info("高级搜索请求: {}", request);
        
        try {
            Page<WordDocument> results;
            
            // 根据不同的搜索条件选择相应的搜索方法
            if (request.getTitle() != null && request.getContent() != null && request.getCategory() != null) {
                results = wordSearchService.advancedSearch(
                        request.getTitle(), request.getContent(), request.getCategory(),
                        request.getPage(), request.getSize());
            } else if (request.getTitle() != null) {
                results = wordSearchService.searchByTitle(
                        request.getTitle(), request.getPage(), request.getSize());
            } else if (request.getContent() != null) {
                results = wordSearchService.searchByContent(
                        request.getContent(), request.getPage(), request.getSize());
            } else if (request.getCategory() != null) {
                results = wordSearchService.findByCategory(
                        request.getCategory(), request.getPage(), request.getSize());
            } else if (request.getAuthor() != null) {
                results = wordSearchService.findByAuthor(
                        request.getAuthor(), request.getPage(), request.getSize());
            } else {
                results = wordSearchService.searchByKeyword(
                        request.getKeyword(), request.getPage(), request.getSize());
            }
            
            return ResponseEntity.ok(ApiResponse.success("高级搜索成功", results));
        } catch (Exception e) {
            logger.error("高级搜索失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("高级搜索失败: " + e.getMessage()));
        }
    }
    
    /**
     * 根据分类查找文档
     * GET /api/v1/search/category/{category}
     */
    @GetMapping("/category/{category}")
    public ResponseEntity<ApiResponse<Page<WordDocument>>> findByCategory(
            @PathVariable String category,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        logger.info("分类查找 - 分类: {}", category);
        
        try {
            Page<WordDocument> results = wordSearchService.findByCategory(category, page, size);
            return ResponseEntity.ok(ApiResponse.success("分类查找成功", results));
        } catch (Exception e) {
            logger.error("分类查找失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("分类查找失败: " + e.getMessage()));
        }
    }
    
    /**
     * 根据标签查找文档
     * GET /api/v1/search/tag/{tag}
     */
    @GetMapping("/tag/{tag}")
    public ResponseEntity<ApiResponse<List<WordDocument>>> findByTag(@PathVariable String tag) {
        
        logger.info("标签查找 - 标签: {}", tag);
        
        try {
            List<WordDocument> results = wordSearchService.findByTag(tag);
            return ResponseEntity.ok(ApiResponse.success("标签查找成功", results));
        } catch (Exception e) {
            logger.error("标签查找失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("标签查找失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取所有文档
     * GET /api/v1/search/all
     */
    @GetMapping("/all")
    public ResponseEntity<ApiResponse<Page<WordDocument>>> findAll(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        logger.info("获取所有文档 - 页码: {}, 大小: {}", page, size);
        
        try {
            Page<WordDocument> results = wordSearchService.findAllDocuments(page, size);
            return ResponseEntity.ok(ApiResponse.success("获取成功", results));
        } catch (Exception e) {
            logger.error("获取所有文档失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取失败: " + e.getMessage()));
        }
    }
    
    /**
     * 根据ID获取文档详情
     * GET /api/v1/search/document/{id}
     */
    @GetMapping("/document/{id}")
    public ResponseEntity<ApiResponse<WordDocument>> getDocument(@PathVariable String id) {
        
        logger.info("获取文档详情 - ID: {}", id);
        
        try {
            Optional<WordDocument> document = wordSearchService.findById(id);
            if (document.isPresent()) {
                // 增加浏览次数
                wordSearchService.incrementViewCount(id);
                return ResponseEntity.ok(ApiResponse.success("获取成功", document.get()));
            } else {
                return ResponseEntity.notFound()
                        .build();
            }
        } catch (Exception e) {
            logger.error("获取文档详情失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取失败: " + e.getMessage()));
        }
    }
}
