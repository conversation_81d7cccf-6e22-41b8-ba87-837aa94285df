package com.howbuy.ai.entity;

import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.MultiField;
import org.springframework.data.elasticsearch.annotations.InnerField;

/**
 * 金融产品实体类
 * 对应ai_main_search索引
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(exclude = {"weight"})
@ToString(exclude = {"rtlx"}) // 排除可能很长的日期字段
@Document(indexName = "ai_main_search")
public class FinancialProduct {

    /**
     * 产品代码 - 作为文档ID
     */
    @Id
    @MultiField(
        mainField = @Field(type = FieldType.Text, analyzer = "onebyone_analyzer"),
        otherFields = {
            @InnerField(suffix = "keyword", type = FieldType.Keyword)
        }
    )
    private String tscode;

    /**
     * 产品名称
     */
    @MultiField(
        mainField = @Field(type = FieldType.Keyword),
        otherFields = {
            @InnerField(suffix = "selfsearch", type = FieldType.Text, analyzer = "onebyone_analyzer")
        }
    )
    private String tsname;

    /**
     * 产品简称
     */
    @MultiField(
        mainField = @Field(type = FieldType.Keyword),
        otherFields = {
            @InnerField(suffix = "selfsearch", type = FieldType.Text, analyzer = "onebyone_analyzer")
        }
    )
    private String tsshortName;

    /**
     * 产品简拼
     */
    @Field(type = FieldType.Text, analyzer = "onebyone_analyzer")
    private String tsshortPinyin;

    /**
     * 搜索名称（支持拼音搜索）
     */
    @MultiField(
        mainField = @Field(type = FieldType.Text, analyzer = "ik_pinyin_analyzer"),
        otherFields = {
            @InnerField(suffix = "fullpinyin", type = FieldType.Text, analyzer = "full_pinyin_analyzer")
        }
    )
    private String searchName;

    /**
     * 搜索简称（支持拼音搜索）
     */
    @MultiField(
        mainField = @Field(type = FieldType.Text, analyzer = "ik_pinyin_analyzer"),
        otherFields = {
            @InnerField(suffix = "fullpinyin", type = FieldType.Text, analyzer = "full_pinyin_analyzer")
        }
    )
    private String searchShortName;

    /**
     * 产品类型
     */
    @Field(type = FieldType.Keyword)
    private String tsType;

    /**
     * 业务类型
     */
    @Field(type = FieldType.Keyword)
    private String businessType;

    /**
     * 产品属性
     */
    @Field(type = FieldType.Keyword)
    private String attribute;

    /**
     * 申购状态 (0-不可申购, 1-可申购)
     */
    @Field(type = FieldType.Long)
    @Builder.Default
    private Long opBuyStatus = 0L;

    /**
     * 赎回状态 (0-不可赎回, 1-可赎回)
     */
    @Field(type = FieldType.Long)
    @Builder.Default
    private Long opRedeemStatus = 0L;

    /**
     * 销售标志
     */
    @Field(type = FieldType.Long)
    @Builder.Default
    private Long xsbz = 0L;

    /**
     * 展示标志
     */
    @Field(type = FieldType.Long)
    @Builder.Default
    private Long zsbz = 0L;

    /**
     * 权重（用于排序）
     */
    @Field(type = FieldType.Long)
    @Builder.Default
    private Long weight = 0L;

    /**
     * 日期类型
     */
    @Field(type = FieldType.Text)
    private String rtlx;

    /**
     * 便捷构造函数
     */
    public FinancialProduct(String tscode, String tsname, String tsType) {
        this.tscode = tscode;
        this.tsname = tsname;
        this.tsType = tsType;
        this.opBuyStatus = 0L;
        this.opRedeemStatus = 0L;
        this.xsbz = 0L;
        this.zsbz = 0L;
        this.weight = 0L;
    }

    /**
     * 检查产品是否可申购
     */
    public boolean isBuyable() {
        return opBuyStatus != null && opBuyStatus == 1L;
    }

    /**
     * 检查产品是否可赎回
     */
    public boolean isRedeemable() {
        return opRedeemStatus != null && opRedeemStatus == 1L;
    }

    /**
     * 检查产品是否在售
     */
    public boolean isOnSale() {
        return xsbz != null && xsbz == 1L;
    }

    /**
     * 检查产品是否展示
     */
    public boolean isDisplayed() {
        return zsbz != null && zsbz == 1L;
    }
}
