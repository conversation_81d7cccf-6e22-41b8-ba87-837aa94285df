package com.howbuy.ai.entity;

import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.LocalDateTime;

/**
 * 词汇文档实体类
 * 用于存储在Elasticsearch中的文档结构
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(exclude = {"createTime", "updateTime", "viewCount"})
@ToString(exclude = {"content"}) // 排除content字段避免日志过长
@Document(indexName = "word_documents")
public class WordDocument {
    
    @Id
    private String id;

    @Field(type = FieldType.Text, analyzer = "standard")
    private String title;

    @Field(type = FieldType.Text, analyzer = "standard")
    private String content;

    @Field(type = FieldType.Keyword)
    private String category;

    @Field(type = FieldType.Keyword)
    private String[] tags;

    @Field(type = FieldType.Date)
    @Builder.Default
    private LocalDateTime createTime = LocalDateTime.now();

    @Field(type = FieldType.Date)
    @Builder.Default
    private LocalDateTime updateTime = LocalDateTime.now();

    @Field(type = FieldType.Keyword)
    private String author;

    @Field(type = FieldType.Integer)
    @Builder.Default
    private Integer viewCount = 0;

    /**
     * 便捷构造函数，用于快速创建文档
     */
    public WordDocument(String title, String content, String category) {
        this.title = title;
        this.content = content;
        this.category = category;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        this.viewCount = 0;
    }
}
