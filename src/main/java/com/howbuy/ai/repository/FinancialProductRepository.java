package com.howbuy.ai.repository;

import com.howbuy.ai.entity.FinancialProduct;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.annotations.Query;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

/** 金融产品Repository接口 */
@Repository
public interface FinancialProductRepository
    extends ElasticsearchRepository<FinancialProduct, String> {

  /** 根据产品代码查找（精确匹配） */
  FinancialProduct findByTscode(String tscode);

  /** 根据产品名称查找（精确匹配） */
  List<FinancialProduct> findByTsname(String tsname);

  /** 根据产品类型查找 */
  Page<FinancialProduct> findByTsType(String tsType, Pageable pageable);

  /** 根据业务类型查找 */
  Page<FinancialProduct> findByBusinessType(String businessType, Pageable pageable);

  /** 根据申购状态查找 */
  Page<FinancialProduct> findByOpBuyStatus(Long opBuyStatus, Pageable pageable);

  /** 查找可申购的产品 */
  @Query(
      "{\"bool\": {\"must\": [{\"term\": {\"op_buy_status\": 1}}, {\"term\": {\"xsbz\": 1}}, {\"term\": {\"zsbz\": 1}}]}}")
  Page<FinancialProduct> findBuyableProducts(Pageable pageable);

  /** 查找可赎回的产品 */
  @Query(
      "{\"bool\": {\"must\": [{\"term\": {\"op_redeem_status\": 1}}, {\"term\": {\"xsbz\": 1}}]}}")
  Page<FinancialProduct> findRedeemableProducts(Pageable pageable);

  /** 根据产品代码前缀搜索 */
  @Query("{\"prefix\": {\"tscode.keyword\": \"?0\"}}")
  Page<FinancialProduct> findByTscodePrefix(String prefix, Pageable pageable);

  /** 根据产品名称模糊搜索 */
  @Query("{\"wildcard\": {\"tsname\": \"*?0*\"}}")
  Page<FinancialProduct> findByTsnameFuzzy(String name, Pageable pageable);

  /** 综合搜索（产品名称、代码、拼音） */
  @Query(
      "{"
          + "\"bool\": {"
          + "\"should\": ["
          + "{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"searchName^3\", \"searchShortName^2\", \"tsname.selfsearch\", \"tsshortName.selfsearch\"], \"type\": \"best_fields\", \"fuzziness\": \"AUTO\"}},"
          + "{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"searchName.fullpinyin^2\", \"searchShortName.fullpinyin\"], \"type\": \"phrase_prefix\"}},"
          + "{\"prefix\": {\"tscode.keyword\": \"?0\"}}"
          + "], \"minimum_should_match\": 1}"
          + "}")
  Page<FinancialProduct> comprehensiveSearch(String keyword, Pageable pageable);

  /** 拼音搜索 */
  @Query(
      "{"
          + "\"bool\": {"
          + "\"should\": ["
          + "{\"match\": {\"searchName\": {\"query\": \"?0\", \"analyzer\": \"ik_pinyin_analyzer\"}}},"
          + "{\"match\": {\"searchShortName\": {\"query\": \"?0\", \"analyzer\": \"ik_pinyin_analyzer\"}}},"
          + "{\"match\": {\"searchName.fullpinyin\": {\"query\": \"?0\", \"analyzer\": \"full_pinyin_analyzer\"}}},"
          + "{\"match\": {\"tsshortPinyin\": {\"query\": \"?0\", \"analyzer\": \"onebyone_analyzer\"}}}"
          + "], \"minimum_should_match\": 1}"
          + "}")
  Page<FinancialProduct> pinyinSearch(String keyword, Pageable pageable);

  /** 高级搜索 */
  @Query(
      "{"
          + "\"bool\": {"
          + "\"must\": [{"
          + "\"bool\": {"
          + "\"should\": ["
          + "{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"searchName^3\", \"searchShortName^2\", \"tsname.selfsearch\", \"tsshortName.selfsearch\", \"tscode\"], \"type\": \"best_fields\"}},"
          + "{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"searchName.fullpinyin^2\", \"searchShortName.fullpinyin\", \"tsshortPinyin\"], \"type\": \"phrase_prefix\"}}"
          + "], \"minimum_should_match\": 1}"
          + "}], \"filter\": [?1]}"
          + "}")
  Page<FinancialProduct> advancedSearch(String keyword, String filters, Pageable pageable);

  /** 根据权重范围查找 */
  @Query("{\"range\": {\"weight\": {\"gte\": ?0, \"lte\": ?1}}}")
  Page<FinancialProduct> findByWeightRange(Long minWeight, Long maxWeight, Pageable pageable);

  /** 根据多个产品类型查找 */
  @Query("{\"terms\": {\"tsType\": ?0}}")
  Page<FinancialProduct> findByTsTypeIn(List<String> tsTypes, Pageable pageable);

  /** 根据多个业务类型查找 */
  @Query("{\"terms\": {\"businessType\": ?0}}")
  Page<FinancialProduct> findByBusinessTypeIn(List<String> businessTypes, Pageable pageable);

  /** 统计各产品类型的数量 */
  @Query(
      "{"
          + "\"size\": 0,"
          + "\"aggs\": {"
          + "\"tsType_count\": {"
          + "\"terms\": {"
          + "\"field\": \"tsType\","
          + "\"size\": 100"
          + "}"
          + "}"
          + "}"
          + "}")
  List<FinancialProduct> countByTsType();

  /** 获取热门产品（按权重排序） */
  @Query("{\"bool\": {\"must\": [{\"term\": {\"zsbz\": 1}}]}}")
  Page<FinancialProduct> findPopularProducts(Pageable pageable);
}
