package com.howbuy.ai.repository;

import com.howbuy.ai.entity.WordDocument;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.annotations.Query;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

/** WordDocument Elasticsearch Repository 提供基本的CRUD操作和自定义查询方法 */
@Repository
public interface WordDocumentRepository extends ElasticsearchRepository<WordDocument, String> {

  /**
   * 根据标题搜索文档
   *
   * @param title 标题关键词
   * @param pageable 分页参数
   * @return 分页结果
   */
  Page<WordDocument> findByTitleContaining(String title, Pageable pageable);

  /**
   * 根据内容搜索文档
   *
   * @param content 内容关键词
   * @param pageable 分页参数
   * @return 分页结果
   */
  Page<WordDocument> findByContentContaining(String content, Pageable pageable);

  /**
   * 根据分类查找文档
   *
   * @param category 分类
   * @param pageable 分页参数
   * @return 分页结果
   */
  Page<WordDocument> findByCategory(String category, Pageable pageable);

  /**
   * 根据作者查找文档
   *
   * @param author 作者
   * @param pageable 分页参数
   * @return 分页结果
   */
  Page<WordDocument> findByAuthor(String author, Pageable pageable);

  /**
   * 多字段搜索 - 在标题和内容中搜索关键词
   *
   * @param keyword 搜索关键词
   * @param pageable 分页参数
   * @return 分页结果
   */
  @Query("{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"title^2\", \"content\"]}}")
  Page<WordDocument> searchByKeyword(String keyword, Pageable pageable);

  /**
   * 高级搜索 - 支持标题、内容、分类的组合搜索
   *
   * @param title 标题关键词
   * @param content 内容关键词
   * @param category 分类
   * @param pageable 分页参数
   * @return 分页结果
   */
  @Query(
      "{\"bool\": {\"must\": ["
          + "{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"title\"], \"operator\": \"and\"}},"
          + "{\"multi_match\": {\"query\": \"?1\", \"fields\": [\"content\"], \"operator\": \"and\"}},"
          + "{\"term\": {\"category\": \"?2\"}}"
          + "]}}")
  Page<WordDocument> advancedSearch(
      String title, String content, String category, Pageable pageable);

  /**
   * 根据标签查找文档
   *
   * @param tag 标签
   * @return 文档列表
   */
  List<WordDocument> findByTagsContaining(String tag);
}
