package com.howbuy.ai.search;

import com.howbuy.ai.util.NumberConversionUtil;
import lombok.extern.log4j.Log4j2;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/** 高级搜索查询构建器 用于构建复杂的Elasticsearch查询，支持中文数字转换和多字段匹配 */
@Log4j2
@Component
public class AdvancedSearchQueryBuilder {

  /** 默认的短语查询slop值 */
  private static final int DEFAULT_SLOP = 3;

  /** 搜索字段权重配置 */
  private static final float TSCODE_BOOST = 3.0f;

  private static final float SEARCH_NAME_BOOST = 2.5f;
  private static final float SEARCH_SHORT_NAME_BOOST = 2.0f;
  private static final float PINYIN_BOOST = 1.5f;
  private static final float SELF_SEARCH_BOOST = 1.0f;

  /**
   * 构建高级搜索查询
   *
   * @param query 搜索关键词
   * @return Elasticsearch查询构建器
   */
  public QueryBuilder buildAdvancedSearchQuery(String query) {
    return buildAdvancedSearchQuery(query, DEFAULT_SLOP);
  }

  /**
   * 构建高级搜索查询（带自定义slop值）
   *
   * @param query 搜索关键词
   * @param slop 短语查询的slop值
   * @return Elasticsearch查询构建器
   */
  public QueryBuilder buildAdvancedSearchQuery(String query, int slop) {
    if (!StringUtils.hasText(query)) {
      log.warn("搜索关键词为空，返回match_all查询");
      return QueryBuilders.matchAllQuery();
    }

    // 验证slop值
    slop = validateSlop(slop);

    try {
      log.info("构建高级搜索查询，关键词: {}, slop: {}", query, slop);

      // 创建主查询构建器
      BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();

      // 创建内容查询构建器
      BoolQueryBuilder contentQueryBuilder = QueryBuilders.boolQuery();

      // 处理数字转中文
      String chineseQuery = NumberConversionUtil.replaceNumbersWithChinese(query);
      log.debug("数字转换结果: {} -> {}", query, chineseQuery);

      // 添加各种匹配条件
      addExactMatches(contentQueryBuilder, query);
      addPhraseMatches(contentQueryBuilder, chineseQuery, slop);
      addPinyinMatches(contentQueryBuilder, chineseQuery);
      addSelfSearchMatches(contentQueryBuilder, query, slop);

      // 设置最小匹配数
      contentQueryBuilder.minimumShouldMatch(1);

      // 将内容查询作为必须条件
      mainQueryBuilder.must(contentQueryBuilder);

      log.debug("高级搜索查询构建完成");
      return mainQueryBuilder;

    } catch (Exception e) {
      log.error("构建高级搜索查询时发生错误，关键词: {}", query, e);
      // 发生错误时返回简单的match查询作为降级方案
      return QueryBuilders.multiMatchQuery(query, "searchName", "tsname", "tscode");
    }
  }

  /**
   * 添加精确匹配条件
   *
   * @param queryBuilder 查询构建器
   * @param query 搜索关键词
   */
  private void addExactMatches(BoolQueryBuilder queryBuilder, String query) {
    // 产品代码精确匹配（最高权重）
    queryBuilder.should(QueryBuilders.matchPhraseQuery("tscode", query).boost(TSCODE_BOOST));

    // 产品简拼匹配
    queryBuilder.should(QueryBuilders.matchPhraseQuery("tsshortPinyin", query).boost(PINYIN_BOOST));
  }

  /**
   * 添加短语匹配条件
   *
   * @param queryBuilder 查询构建器
   * @param searchName 处理后的搜索名称
   * @param slop 短语查询slop值
   */
  private void addPhraseMatches(BoolQueryBuilder queryBuilder, String searchName, int slop) {
    // 搜索名称短语匹配
    queryBuilder.should(
        QueryBuilders.matchPhraseQuery("searchName", searchName)
            .slop(slop)
            .boost(SEARCH_NAME_BOOST));

    // 搜索简称短语匹配
    queryBuilder.should(
        QueryBuilders.matchPhraseQuery("searchShortName", searchName)
            .slop(slop)
            .boost(SEARCH_SHORT_NAME_BOOST));
  }

  /**
   * 添加拼音匹配条件
   *
   * @param queryBuilder 查询构建器
   * @param searchName 处理后的搜索名称
   */
  private void addPinyinMatches(BoolQueryBuilder queryBuilder, String searchName) {
    // 全拼音匹配 - 搜索名称
    queryBuilder.should(
        QueryBuilders.termQuery("searchName.fullpinyin", searchName).boost(PINYIN_BOOST));

    // 全拼音匹配 - 搜索简称
    queryBuilder.should(
        QueryBuilders.termQuery("searchShortName.fullpinyin", searchName).boost(PINYIN_BOOST));
  }

  /**
   * 添加自搜索字段匹配条件
   *
   * @param queryBuilder 查询构建器
   * @param query 原始搜索关键词
   * @param slop 短语查询slop值
   */
  private void addSelfSearchMatches(BoolQueryBuilder queryBuilder, String query, int slop) {
    // 产品简称自搜索匹配
    queryBuilder.should(
        QueryBuilders.matchPhraseQuery("tsshortName.selfsearch", query)
            .slop(slop)
            .boost(SELF_SEARCH_BOOST));

    // 产品名称自搜索匹配
    queryBuilder.should(
        QueryBuilders.matchPhraseQuery("tsname.selfsearch", query)
            .slop(slop)
            .boost(SELF_SEARCH_BOOST));
  }

  /**
   * 构建带过滤条件的高级搜索查询
   *
   * @param query 搜索关键词
   * @param filters 过滤条件
   * @return Elasticsearch查询构建器
   */
  public QueryBuilder buildAdvancedSearchQueryWithFilters(String query, QueryBuilder filters) {
    QueryBuilder searchQuery = buildAdvancedSearchQuery(query);

    if (filters != null) {
      BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
      boolQuery.must(searchQuery);
      boolQuery.filter(filters);
      return boolQuery;
    }

    return searchQuery;
  }

  /**
   * 构建模糊搜索查询（降级方案）
   *
   * @param query 搜索关键词
   * @return Elasticsearch查询构建器
   */
  public QueryBuilder buildFuzzySearchQuery(String query) {
    if (!StringUtils.hasText(query)) {
      return QueryBuilders.matchAllQuery();
    }

    log.info("构建模糊搜索查询，关键词: {}", query);

    return QueryBuilders.multiMatchQuery(query)
        .field("searchName", 2.0f)
        .field("searchShortName", 1.5f)
        .field("tsname", 1.0f)
        .field("tscode", 3.0f)
        .fuzziness("AUTO")
        .prefixLength(1)
        .maxExpansions(50);
  }

  /**
   * 验证slop值的有效性
   *
   * @param slop slop值
   * @return 有效的slop值
   */
  private int validateSlop(int slop) {
    if (slop < 0) {
      log.warn("slop值不能为负数，使用默认值: {}", DEFAULT_SLOP);
      return DEFAULT_SLOP;
    }
    if (slop > 10) {
      log.warn("slop值过大，使用默认值: {}", DEFAULT_SLOP);
      return DEFAULT_SLOP;
    }
    return slop;
  }
}
