package com.howbuy.ai.exception;

import com.howbuy.ai.dto.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Set;

/**
 * 全局异常处理器
 * 统一处理应用程序中的异常
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Object>> handleValidationException(MethodArgumentNotValidException e) {
        log.warn("参数验证失败", e);
        
        StringBuilder message = new StringBuilder("参数验证失败: ");
        for (FieldError error : e.getBindingResult().getFieldErrors()) {
            message.append(error.getField()).append(" ").append(error.getDefaultMessage()).append("; ");
        }
        
        return ResponseEntity.badRequest()
                .body(ApiResponse.badRequest(message.toString()));
    }
    
    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<ApiResponse<Object>> handleBindException(BindException e) {
        log.warn("参数绑定失败", e);
        
        StringBuilder message = new StringBuilder("参数绑定失败: ");
        for (FieldError error : e.getBindingResult().getFieldErrors()) {
            message.append(error.getField()).append(" ").append(error.getDefaultMessage()).append("; ");
        }
        
        return ResponseEntity.badRequest()
                .body(ApiResponse.badRequest(message.toString()));
    }
    
    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ApiResponse<Object>> handleConstraintViolationException(ConstraintViolationException e) {
        log.warn("约束验证失败", e);
        
        StringBuilder message = new StringBuilder("约束验证失败: ");
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        for (ConstraintViolation<?> violation : violations) {
            message.append(violation.getPropertyPath()).append(" ").append(violation.getMessage()).append("; ");
        }
        
        return ResponseEntity.badRequest()
                .body(ApiResponse.badRequest(message.toString()));
    }
    
    /**
     * 处理IllegalArgumentException
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponse<Object>> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("非法参数异常", e);
        return ResponseEntity.badRequest()
                .body(ApiResponse.badRequest("参数错误: " + e.getMessage()));
    }
    
    /**
     * 处理Elasticsearch相关异常
     */
    @ExceptionHandler(org.elasticsearch.ElasticsearchException.class)
    public ResponseEntity<ApiResponse<Object>> handleElasticsearchException(
            org.elasticsearch.ElasticsearchException e) {
        log.error("Elasticsearch异常", e);
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .body(ApiResponse.error(503, "搜索服务暂时不可用: " + e.getMessage()));
    }
    
    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleGenericException(Exception e) {
        log.error("未处理的异常", e);
        return ResponseEntity.internalServerError()
                .body(ApiResponse.error("服务器内部错误: " + e.getMessage()));
    }
}
