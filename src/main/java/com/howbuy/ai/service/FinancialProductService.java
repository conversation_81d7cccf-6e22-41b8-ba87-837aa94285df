package com.howbuy.ai.service;

import com.howbuy.ai.dto.FinancialProductSearchRequest;
import com.howbuy.ai.entity.FinancialProduct;
import com.howbuy.ai.repository.FinancialProductRepository;
import com.howbuy.ai.util.PerformanceLogger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.extern.log4j.Log4j2;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/** 金融产品搜索服务类 */
@Log4j2
@Service
public class FinancialProductService {

  @Autowired private FinancialProductRepository repository;

  @Autowired private ElasticsearchRestTemplate elasticsearchTemplate;

  /** 根据产品代码查找产品 */
  public Optional<FinancialProduct> findByTscode(String tscode) {
    PerformanceLogger.PerformanceMonitor monitor =
        new PerformanceLogger.PerformanceMonitor("findByTscode", tscode);

    try {
      log.info("根据产品代码查找产品: {}", tscode);
      FinancialProduct product = repository.findByTscode(tscode);
      monitor.endWithResult(product != null ? "找到产品" : "未找到产品");
      return Optional.ofNullable(product);
    } catch (Exception e) {
      log.error("根据产品代码查找产品失败: {}", tscode, e);
      monitor.end();
      throw e;
    }
  }

  /** 综合搜索 */
  public Page<FinancialProduct> search(FinancialProductSearchRequest request) {
    PerformanceLogger.PerformanceMonitor monitor =
        new PerformanceLogger.PerformanceMonitor(
            "search", request.getKeyword(), request.getSearchType());

    try {
      log.info("金融产品搜索: {}", request);

      Pageable pageable = createPageable(request);
      Page<FinancialProduct> result;

      switch (request.getSearchType()) {
        case EXACT:
          result = exactSearch(request, pageable);
          break;
        case PREFIX:
          result = prefixSearch(request, pageable);
          break;
        case PINYIN:
          result = pinyinSearch(request, pageable);
          break;
        case FUZZY:
          result = fuzzySearch(request, pageable);
          break;
        case COMPREHENSIVE:
        default:
          result = comprehensiveSearch(request, pageable);
          break;
      }

      PerformanceLogger.logSearchPerformance(
          "financial_product",
          request.getKeyword(),
          (int) result.getTotalElements(),
          System.currentTimeMillis());

      monitor.endWithResult("找到 " + result.getTotalElements() + " 条结果");
      return result;

    } catch (Exception e) {
      log.error("金融产品搜索失败: {}", request, e);
      monitor.end();
      throw e;
    }
  }

  /** 精确搜索 */
  private Page<FinancialProduct> exactSearch(
      FinancialProductSearchRequest request, Pageable pageable) {
    if (StringUtils.hasText(request.getTscode())) {
      FinancialProduct product = repository.findByTscode(request.getTscode());
      if (product != null) {
        return new PageImpl<>(Collections.singletonList(product), pageable, 1);
      } else {
        return new PageImpl<>(Collections.emptyList(), pageable, 0);
      }
    }

    if (StringUtils.hasText(request.getTsname())) {
      List<FinancialProduct> products = repository.findByTsname(request.getTsname());
      return new PageImpl<>(products, pageable, products.size());
    }

    return buildAdvancedQuery(request, pageable);
  }

  /** 前缀搜索 */
  private Page<FinancialProduct> prefixSearch(
      FinancialProductSearchRequest request, Pageable pageable) {
    if (StringUtils.hasText(request.getKeyword())) {
      return repository.findByTscodePrefix(request.getKeyword(), pageable);
    }
    return buildAdvancedQuery(request, pageable);
  }

  /** 拼音搜索 */
  private Page<FinancialProduct> pinyinSearch(
      FinancialProductSearchRequest request, Pageable pageable) {
    if (StringUtils.hasText(request.getKeyword())) {
      return repository.pinyinSearch(request.getKeyword(), pageable);
    }
    return buildAdvancedQuery(request, pageable);
  }

  /** 模糊搜索 */
  private Page<FinancialProduct> fuzzySearch(
      FinancialProductSearchRequest request, Pageable pageable) {
    if (StringUtils.hasText(request.getKeyword())) {
      return repository.findByTsnameFuzzy(request.getKeyword(), pageable);
    }
    return buildAdvancedQuery(request, pageable);
  }

  /** 综合搜索 */
  private Page<FinancialProduct> comprehensiveSearch(
      FinancialProductSearchRequest request, Pageable pageable) {
    if (StringUtils.hasText(request.getKeyword())) {
      return repository.comprehensiveSearch(request.getKeyword(), pageable);
    }
    return buildAdvancedQuery(request, pageable);
  }

  /** 构建高级查询 */
  private Page<FinancialProduct> buildAdvancedQuery(
      FinancialProductSearchRequest request, Pageable pageable) {
    BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

    // 关键词搜索
    if (StringUtils.hasText(request.getKeyword())) {
      BoolQueryBuilder keywordQuery =
          QueryBuilders.boolQuery()
              .should(
                  QueryBuilders.multiMatchQuery(request.getKeyword())
                      .field("searchName", 3.0f)
                      .field("searchShortName", 2.0f)
                      .field("tsname.selfsearch")
                      .field("tsshortName.selfsearch")
                      .field("tscode"))
              .should(
                  QueryBuilders.multiMatchQuery(request.getKeyword())
                      .field("searchName.fullpinyin", 2.0f)
                      .field("searchShortName.fullpinyin")
                      .field("tsshortPinyin"))
              .minimumShouldMatch(1);
      boolQuery.must(keywordQuery);
    }

    // 产品类型过滤
    if (request.getTsTypes() != null && !request.getTsTypes().isEmpty()) {
      boolQuery.filter(QueryBuilders.termsQuery("tsType", request.getTsTypes()));
    }

    // 业务类型过滤
    if (request.getBusinessTypes() != null && !request.getBusinessTypes().isEmpty()) {
      boolQuery.filter(QueryBuilders.termsQuery("businessType", request.getBusinessTypes()));
    }

    // 产品属性过滤
    if (request.getAttributes() != null && !request.getAttributes().isEmpty()) {
      boolQuery.filter(QueryBuilders.termsQuery("attribute", request.getAttributes()));
    }

    // 状态过滤
    addStatusFilters(boolQuery, request);

    // 权重范围过滤
    addWeightRangeFilter(boolQuery, request);

    NativeSearchQuery searchQuery =
        new NativeSearchQueryBuilder().withQuery(boolQuery).withPageable(pageable).build();

    SearchHits<FinancialProduct> searchHits =
        elasticsearchTemplate.search(searchQuery, FinancialProduct.class);

    // 手动转换SearchHits为Page
    List<FinancialProduct> content = new ArrayList<>();
    for (SearchHit<FinancialProduct> hit : searchHits) {
      content.add(hit.getContent());
    }

    return new PageImpl<>(content, pageable, searchHits.getTotalHits());
  }

  /** 添加状态过滤条件 */
  private void addStatusFilters(BoolQueryBuilder boolQuery, FinancialProductSearchRequest request) {
    if (request.getOpBuyStatus() != null) {
      boolQuery.filter(QueryBuilders.termQuery("op_buy_status", request.getOpBuyStatus()));
    }

    if (request.getOpRedeemStatus() != null) {
      boolQuery.filter(QueryBuilders.termQuery("op_redeem_status", request.getOpRedeemStatus()));
    }

    if (request.getXsbz() != null) {
      boolQuery.filter(QueryBuilders.termQuery("xsbz", request.getXsbz()));
    }

    if (request.getZsbz() != null) {
      boolQuery.filter(QueryBuilders.termQuery("zsbz", request.getZsbz()));
    }
  }

  /** 添加权重范围过滤条件 */
  private void addWeightRangeFilter(
      BoolQueryBuilder boolQuery, FinancialProductSearchRequest request) {
    if (request.getMinWeight() != null || request.getMaxWeight() != null) {
      RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("weight");
      if (request.getMinWeight() != null) {
        rangeQuery.gte(request.getMinWeight());
      }
      if (request.getMaxWeight() != null) {
        rangeQuery.lte(request.getMaxWeight());
      }
      boolQuery.filter(rangeQuery);
    }
  }

  /** 创建分页对象 */
  private Pageable createPageable(FinancialProductSearchRequest request) {
    Sort sort = createSort(request);
    return PageRequest.of(request.getPage(), request.getSize(), sort);
  }

  /** 创建排序对象 */
  private Sort createSort(FinancialProductSearchRequest request) {
    Sort.Direction direction =
        request.getSortDirection() == FinancialProductSearchRequest.SortDirection.ASC
            ? Sort.Direction.ASC
            : Sort.Direction.DESC;

    return Sort.by(direction, request.getSortField().getFieldName());
  }

  /** 获取可申购产品 */
  public Page<FinancialProduct> getBuyableProducts(int page, int size) {
    log.info("获取可申购产品，页码: {}, 大小: {}", page, size);
    Pageable pageable = PageRequest.of(page, size, Sort.by("weight").descending());
    return repository.findBuyableProducts(pageable);
  }

  /** 获取可赎回产品 */
  public Page<FinancialProduct> getRedeemableProducts(int page, int size) {
    log.info("获取可赎回产品，页码: {}, 大小: {}", page, size);
    Pageable pageable = PageRequest.of(page, size, Sort.by("weight").descending());
    return repository.findRedeemableProducts(pageable);
  }

  /** 获取热门产品 */
  public Page<FinancialProduct> getPopularProducts(int page, int size) {
    log.info("获取热门产品，页码: {}, 大小: {}", page, size);
    Pageable pageable = PageRequest.of(page, size, Sort.by("weight").descending());
    return repository.findPopularProducts(pageable);
  }

  /** 根据产品类型获取产品 */
  public Page<FinancialProduct> getProductsByType(String tsType, int page, int size) {
    log.info("根据产品类型获取产品: {}", tsType);
    Pageable pageable = PageRequest.of(page, size, Sort.by("weight").descending());
    return repository.findByTsType(tsType, pageable);
  }
}
