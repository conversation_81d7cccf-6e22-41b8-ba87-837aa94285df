package com.howbuy.ai.service;

import com.howbuy.ai.entity.WordDocument;
import com.howbuy.ai.repository.WordDocumentRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 词汇搜索服务类
 * 提供文档的增删改查和搜索功能
 */
@Service
public class WordSearchService {
    
    private static final Logger logger = LoggerFactory.getLogger(WordSearchService.class);
    
    @Autowired
    private WordDocumentRepository wordDocumentRepository;
    
    /**
     * 保存文档
     * @param document 文档对象
     * @return 保存后的文档
     */
    public WordDocument saveDocument(WordDocument document) {
        logger.info("保存文档: {}", document.getTitle());
        document.setUpdateTime(LocalDateTime.now());
        return wordDocumentRepository.save(document);
    }
    
    /**
     * 根据ID查找文档
     * @param id 文档ID
     * @return 文档对象
     */
    public Optional<WordDocument> findById(String id) {
        logger.debug("根据ID查找文档: {}", id);
        return wordDocumentRepository.findById(id);
    }
    
    /**
     * 删除文档
     * @param id 文档ID
     */
    public void deleteDocument(String id) {
        logger.info("删除文档: {}", id);
        wordDocumentRepository.deleteById(id);
    }
    
    /**
     * 获取所有文档（分页）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 分页结果
     */
    public Page<WordDocument> findAllDocuments(int page, int size) {
        logger.debug("获取所有文档，页码: {}, 大小: {}", page, size);
        Pageable pageable = PageRequest.of(page, size, Sort.by("createTime").descending());
        return wordDocumentRepository.findAll(pageable);
    }
    
    /**
     * 关键词搜索
     * @param keyword 搜索关键词
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    public Page<WordDocument> searchByKeyword(String keyword, int page, int size) {
        logger.info("关键词搜索: {}", keyword);
        Pageable pageable = PageRequest.of(page, size, Sort.by("_score").descending());
        return wordDocumentRepository.searchByKeyword(keyword, pageable);
    }
    
    /**
     * 根据标题搜索
     * @param title 标题关键词
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    public Page<WordDocument> searchByTitle(String title, int page, int size) {
        logger.info("标题搜索: {}", title);
        Pageable pageable = PageRequest.of(page, size, Sort.by("createTime").descending());
        return wordDocumentRepository.findByTitleContaining(title, pageable);
    }
    
    /**
     * 根据内容搜索
     * @param content 内容关键词
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    public Page<WordDocument> searchByContent(String content, int page, int size) {
        logger.info("内容搜索: {}", content);
        Pageable pageable = PageRequest.of(page, size, Sort.by("createTime").descending());
        return wordDocumentRepository.findByContentContaining(content, pageable);
    }
    
    /**
     * 根据分类查找
     * @param category 分类
     * @param page 页码
     * @param size 每页大小
     * @return 查找结果
     */
    public Page<WordDocument> findByCategory(String category, int page, int size) {
        logger.info("分类查找: {}", category);
        Pageable pageable = PageRequest.of(page, size, Sort.by("createTime").descending());
        return wordDocumentRepository.findByCategory(category, pageable);
    }
    
    /**
     * 根据作者查找
     * @param author 作者
     * @param page 页码
     * @param size 每页大小
     * @return 查找结果
     */
    public Page<WordDocument> findByAuthor(String author, int page, int size) {
        logger.info("作者查找: {}", author);
        Pageable pageable = PageRequest.of(page, size, Sort.by("createTime").descending());
        return wordDocumentRepository.findByAuthor(author, pageable);
    }
    
    /**
     * 根据标签查找
     * @param tag 标签
     * @return 文档列表
     */
    public List<WordDocument> findByTag(String tag) {
        logger.info("标签查找: {}", tag);
        return wordDocumentRepository.findByTagsContaining(tag);
    }
    
    /**
     * 高级搜索
     * @param title 标题关键词
     * @param content 内容关键词
     * @param category 分类
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    public Page<WordDocument> advancedSearch(String title, String content, String category, int page, int size) {
        logger.info("高级搜索 - 标题: {}, 内容: {}, 分类: {}", title, content, category);
        Pageable pageable = PageRequest.of(page, size, Sort.by("_score").descending());
        return wordDocumentRepository.advancedSearch(title, content, category, pageable);
    }
    
    /**
     * 更新文档浏览次数
     * @param id 文档ID
     */
    public void incrementViewCount(String id) {
        Optional<WordDocument> optionalDoc = findById(id);
        if (optionalDoc.isPresent()) {
            WordDocument document = optionalDoc.get();
            document.setViewCount(document.getViewCount() + 1);
            saveDocument(document);
            logger.debug("更新文档浏览次数: {}", id);
        }
    }
}
