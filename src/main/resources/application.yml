# Spring Boot应用配置
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: word-search-service
  
  # Elasticsearch配置
  elasticsearch:
    uris: http://localhost:9200
    username: 
    password: 
    connection-timeout: 10s
    socket-timeout: 30s
  
  # <PERSON>配置
  jackson:
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai

# 日志配置
logging:
  level:
    com.howbuy.ai: DEBUG
    org.springframework.data.elasticsearch: DEBUG
    org.elasticsearch: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when_authorized

# 自定义配置
word-search:
  elasticsearch:
    index-name: word_documents
    max-results: 100
    highlight-enabled: true
