# Elasticsearch 现代化配置示例
# 支持新的客户端API和完整的配置选项

spring:
  elasticsearch:
    # 集群节点URI列表（支持多节点）
    uris:
      - http://localhost:9200
      - http://localhost:9201
      # - https://es-cluster.example.com:9200
    
    # 认证配置
    authentication:
      username: ${ES_USERNAME:}
      password: ${ES_PASSWORD:}
    
    # 连接配置
    connection:
      # 连接超时时间
      connect-timeout: 10s
      # Socket读取超时时间
      socket-timeout: 30s
      # 连接保持活跃时间
      keep-alive: 30s
      # 请求超时时间
      request-timeout: 60s
    
    # SSL配置
    ssl:
      # 是否启用SSL
      enabled: false
      # 信任存储路径
      truststore: ${ES_TRUSTSTORE_PATH:}
      # 信任存储密码
      truststore-password: ${ES_TRUSTSTORE_PASSWORD:}
      # 密钥存储路径
      keystore: ${ES_KEYSTORE_PATH:}
      # 密钥存储密码
      keystore-password: ${ES_KEYSTORE_PASSWORD:}
      # 是否验证主机名
      verify-hostnames: true
    
    # 连接池配置
    pool:
      # 最大连接数
      max-connections: 100
      # 每个路由的最大连接数
      max-connections-per-route: 10
      # 连接空闲超时时间
      idle-timeout: 5m
      # 连接生存时间
      time-to-live: 30m

# 日志配置
logging:
  level:
    com.howbuy.ai.config: DEBUG
    org.elasticsearch: INFO
    co.elastic.clients: INFO

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,elasticsearch
  endpoint:
    health:
      show-details: always
  health:
    elasticsearch:
      enabled: true

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  elasticsearch:
    uris:
      - http://localhost:9200
    authentication:
      username: ""
      password: ""
    ssl:
      enabled: false

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
  elasticsearch:
    uris:
      - http://test-es.example.com:9200
    authentication:
      username: ${ES_USERNAME:test_user}
      password: ${ES_PASSWORD:test_pass}
    ssl:
      enabled: true

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  elasticsearch:
    uris:
      - https://prod-es-1.example.com:9200
      - https://prod-es-2.example.com:9200
      - https://prod-es-3.example.com:9200
    authentication:
      username: ${ES_USERNAME}
      password: ${ES_PASSWORD}
    ssl:
      enabled: true
      verify-hostnames: true
    pool:
      max-connections: 200
      max-connections-per-route: 20
    connection:
      connect-timeout: 5s
      socket-timeout: 60s
      request-timeout: 120s
