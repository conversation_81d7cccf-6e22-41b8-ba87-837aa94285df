# 测试环境配置
server:
  port: 0  # 随机端口

spring:
  application:
    name: word-search-service-test
  
  # 测试环境的Elasticsearch配置
  elasticsearch:
    uris: http://localhost:9200
    connection-timeout: 5s
    socket-timeout: 10s

# 日志配置
logging:
  level:
    com.howbuy.ai: DEBUG
    org.springframework.data.elasticsearch: WARN
    org.elasticsearch: WARN
    org.springframework.web: DEBUG

# 禁用数据初始化
word-search:
  data-init:
    enabled: false
