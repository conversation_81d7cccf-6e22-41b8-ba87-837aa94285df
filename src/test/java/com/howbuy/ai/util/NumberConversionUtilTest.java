package com.howbuy.ai.util;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;

/** 数字转换工具类测试 */
public class NumberConversionUtilTest {

  @Test
  void testReplaceNumbersWithChinese_NormalCases() {
    // 基本数字转换
    assertEquals("华夏成长一号", NumberConversionUtil.replaceNumbersWithChinese("华夏成长1号"));
    assertEquals("招商银行二零二三年报", NumberConversionUtil.replaceNumbersWithChinese("招商银行2023年报"));
    assertEquals("基金十号", NumberConversionUtil.replaceNumbersWithChinese("基金10号"));

    // 多个数字
    assertEquals("基金一号二期", NumberConversionUtil.replaceNumbersWithChinese("基金1号2期"));
    assertEquals("产品一二三", NumberConversionUtil.replaceNumbersWithChinese("产品123"));
  }

  @Test
  void testReplaceNumbersWithChinese_EdgeCases() {
    // 空字符串和null
    assertNull(NumberConversionUtil.replaceNumbersWithChinese(null));
    assertEquals("", NumberConversionUtil.replaceNumbersWithChinese(""));
    assertEquals("   ", NumberConversionUtil.replaceNumbersWithChinese("   "));

    // 没有数字的字符串
    assertEquals("华夏成长基金", NumberConversionUtil.replaceNumbersWithChinese("华夏成长基金"));

    // 只有数字的字符串
    assertEquals("一二三", NumberConversionUtil.replaceNumbersWithChinese("123"));

    // 零的处理
    assertEquals("零", NumberConversionUtil.replaceNumbersWithChinese("0"));
    assertEquals("一零一", NumberConversionUtil.replaceNumbersWithChinese("101"));
  }

  @Test
  void testReplaceNumbersWithChinese_LargeNumbers() {
    // 大数字应该保持原样
    String largeNumber = "1000000000"; // 10亿，超出支持范围
    assertEquals(largeNumber, NumberConversionUtil.replaceNumbersWithChinese(largeNumber));

    // 包含大数字的字符串应该只保持大数字原样
    assertEquals("产品1000000000号", NumberConversionUtil.replaceNumbersWithChinese("产品1000000000号"));

    // 混合大数字和小数字
    assertEquals(
        "产品1000000000号第一期", NumberConversionUtil.replaceNumbersWithChinese("产品1000000000号第1期"));

    // 在支持范围内的大数字
    assertEquals("九千九百九十九万九千九百九十九", NumberConversionUtil.replaceNumbersWithChinese("99999999"));
  }

  @ParameterizedTest
  @CsvSource({
    "0, 零",
    "1, 一",
    "10, 十",
    "11, 十一",
    "20, 二十",
    "100, 一百",
    "101, 一百零一",
    "110, 一百一十",
    "1000, 一千",
    "1001, 一千零一",
    "1010, 一千零一十",
    "1100, 一千一百",
    "10000, 一万",
    "10001, 一万零一",
    "10010, 一万零一十",
    "10100, 一万零一百",
    "11000, 一万一千",
    "100000, 十万",
    "1000000, 一百万",
    "10000000, 一千万",
    "100000000, 一亿"
  })
  void testConvertIntegerToChinese_ParameterizedTest(int number, String expected) {
    assertEquals(expected, NumberConversionUtil.convertIntegerToChinese(number));
  }

  @Test
  void testConvertIntegerToChinese_SpecialCases() {
    // 负数
    assertNull(NumberConversionUtil.convertIntegerToChinese(-1));
    assertNull(NumberConversionUtil.convertIntegerToChinese(-100));

    // 超出范围的数字
    assertNull(NumberConversionUtil.convertIntegerToChinese(1000000000)); // 10亿

    // 边界值
    assertEquals("九千九百九十九万九千九百九十九", NumberConversionUtil.convertIntegerToChinese(999999999));
  }

  @Test
  void testConvertIntegerToChinese_ComplexNumbers() {
    // 复杂数字的转换规则测试
    assertEquals("一千二百三十四", NumberConversionUtil.convertIntegerToChinese(1234));
    assertEquals("一万二千三百四十五", NumberConversionUtil.convertIntegerToChinese(12345));
    assertEquals("十二万三千四百五十六", NumberConversionUtil.convertIntegerToChinese(123456));
    assertEquals("一百二十三万四千五百六十七", NumberConversionUtil.convertIntegerToChinese(1234567));
    assertEquals("一千二百三十四万五千六百七十八", NumberConversionUtil.convertIntegerToChinese(12345678));

    // 包含零的复杂数字
    assertEquals("一千零一", NumberConversionUtil.convertIntegerToChinese(1001));
    assertEquals("一万零一", NumberConversionUtil.convertIntegerToChinese(10001));
    assertEquals("十万零一", NumberConversionUtil.convertIntegerToChinese(100001));
    assertEquals("一千万零一", NumberConversionUtil.convertIntegerToChinese(10000001));
  }

  @ParameterizedTest
  @ValueSource(strings = {"123", "abc123def", "产品1号", "2023年", "123abc456"})
  void testContainsNumbers_True(String text) {
    assertTrue(NumberConversionUtil.containsNumbers(text));
  }

  @ParameterizedTest
  @ValueSource(strings = {"abc", "华夏基金", "产品名称", ""})
  void testContainsNumbers_False(String text) {
    assertFalse(NumberConversionUtil.containsNumbers(text));
  }

  @Test
  void testContainsNumbers_NullAndEmpty() {
    assertFalse(NumberConversionUtil.containsNumbers(null));
    assertFalse(NumberConversionUtil.containsNumbers(""));
    assertFalse(NumberConversionUtil.containsNumbers("   "));
  }

  @Test
  void testExtractNumbers() {
    // 基本提取
    assertArrayEquals(new int[] {123}, NumberConversionUtil.extractNumbers("abc123def"));
    assertArrayEquals(new int[] {1, 2, 3}, NumberConversionUtil.extractNumbers("a1b2c3"));
    assertArrayEquals(new int[] {2023, 12, 31}, NumberConversionUtil.extractNumbers("2023年12月31日"));

    // 没有数字
    assertArrayEquals(new int[] {}, NumberConversionUtil.extractNumbers("abc"));
    assertArrayEquals(new int[] {}, NumberConversionUtil.extractNumbers(""));
    assertArrayEquals(new int[] {}, NumberConversionUtil.extractNumbers(null));

    // 大数字处理
    assertArrayEquals(
        new int[] {123}, NumberConversionUtil.extractNumbers("123abc2147483648")); // 第二个数字超出int范围
  }

  @Test
  void testReplaceNumbersWithChinese_RealWorldExamples() {
    // 真实的金融产品名称示例
    assertEquals("华夏成长混合A", NumberConversionUtil.replaceNumbersWithChinese("华夏成长混合A"));
    assertEquals("招商中证白酒指数分级A", NumberConversionUtil.replaceNumbersWithChinese("招商中证白酒指数分级A"));
    assertEquals("易方达蓝筹精选混合", NumberConversionUtil.replaceNumbersWithChinese("易方达蓝筹精选混合"));

    // 包含数字的产品名称
    assertEquals("华夏成长二号混合", NumberConversionUtil.replaceNumbersWithChinese("华夏成长2号混合"));
    assertEquals("招商中证五零零指数", NumberConversionUtil.replaceNumbersWithChinese("招商中证500指数"));
    assertEquals("易方达科技创新三年封闭", NumberConversionUtil.replaceNumbersWithChinese("易方达科技创新3年封闭"));

    // 产品代码（通常不应该转换，但测试功能）
    assertEquals("零零零零零一", NumberConversionUtil.replaceNumbersWithChinese("000001"));
    assertEquals("一一零零一一", NumberConversionUtil.replaceNumbersWithChinese("110011"));
  }

  @Test
  void testPerformance() {
    // 性能测试 - 确保大量调用不会有性能问题
    String testString = "华夏成长1号基金2023年报告";

    long startTime = System.currentTimeMillis();
    for (int i = 0; i < 1000; i++) {
      NumberConversionUtil.replaceNumbersWithChinese(testString);
    }
    long endTime = System.currentTimeMillis();

    // 1000次调用应该在合理时间内完成（比如1秒）
    assertTrue(endTime - startTime < 1000, "性能测试失败，耗时过长");
  }
}
