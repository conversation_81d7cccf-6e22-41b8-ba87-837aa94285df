package com.howbuy.ai.lombok;

import com.howbuy.ai.dto.ApiResponse;
import com.howbuy.ai.dto.SearchRequest;
import com.howbuy.ai.entity.WordDocument;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Lombok集成测试
 * 验证Lombok注解是否正常工作
 * 不依赖Spring Boot上下文，纯粹测试Lombok功能
 */
public class LombokIntegrationTest {

    @Test
    public void testWordDocumentBuilder() {
        // 测试Builder模式
        WordDocument document = WordDocument.builder()
                .title("测试文档")
                .content("这是一个测试文档的内容")
                .category("测试分类")
                .author("测试作者")
                .tags(new String[]{"测试", "Lombok", "Builder"})
                .build();

        // 验证Builder设置的值
        assertEquals("测试文档", document.getTitle());
        assertEquals("这是一个测试文档的内容", document.getContent());
        assertEquals("测试分类", document.getCategory());
        assertEquals("测试作者", document.getAuthor());
        assertArrayEquals(new String[]{"测试", "Lombok", "Builder"}, document.getTags());

        // 验证默认值
        assertNotNull(document.getCreateTime());
        assertNotNull(document.getUpdateTime());
        assertEquals(Integer.valueOf(0), document.getViewCount());
    }

    @Test
    public void testWordDocumentGetterSetter() {
        // 测试Getter/Setter
        WordDocument document = new WordDocument();
        
        document.setTitle("新标题");
        document.setContent("新内容");
        document.setCategory("新分类");
        document.setAuthor("新作者");
        document.setViewCount(100);

        assertEquals("新标题", document.getTitle());
        assertEquals("新内容", document.getContent());
        assertEquals("新分类", document.getCategory());
        assertEquals("新作者", document.getAuthor());
        assertEquals(Integer.valueOf(100), document.getViewCount());
    }

    @Test
    public void testWordDocumentToString() {
        // 测试ToString (排除content字段)
        WordDocument document = WordDocument.builder()
                .title("测试标题")
                .content("这是一个很长的内容，应该被排除在toString之外")
                .category("测试分类")
                .author("测试作者")
                .build();

        String toString = document.toString();
        
        // 验证包含的字段
        assertTrue(toString.contains("测试标题"));
        assertTrue(toString.contains("测试分类"));
        assertTrue(toString.contains("测试作者"));
        
        // 验证排除的字段
        assertFalse(toString.contains("这是一个很长的内容"));
    }

    @Test
    public void testSearchRequestBuilder() {
        // 测试SearchRequest的Builder模式
        SearchRequest request = SearchRequest.builder()
                .keyword("Spring Boot")
                .title("教程")
                .content("详细内容")
                .category("技术文档")
                .author("张三")
                .tag("Java")
                .page(1)
                .size(20)
                .build();

        assertEquals("Spring Boot", request.getKeyword());
        assertEquals("教程", request.getTitle());
        assertEquals("详细内容", request.getContent());
        assertEquals("技术文档", request.getCategory());
        assertEquals("张三", request.getAuthor());
        assertEquals("Java", request.getTag());
        assertEquals(1, request.getPage());
        assertEquals(20, request.getSize());
    }

    @Test
    public void testSearchRequestDefaultValues() {
        // 测试默认值
        SearchRequest request = SearchRequest.builder()
                .keyword("测试关键词")
                .build();

        assertEquals("测试关键词", request.getKeyword());
        assertEquals(0, request.getPage()); // 默认值
        assertEquals(10, request.getSize()); // 默认值
    }

    @Test
    public void testApiResponseBuilder() {
        // 测试ApiResponse的Builder模式
        ApiResponse<String> response = ApiResponse.<String>builder()
                .code(200)
                .message("操作成功")
                .data("测试数据")
                .build();

        assertEquals(200, response.getCode());
        assertEquals("操作成功", response.getMessage());
        assertEquals("测试数据", response.getData());
        assertTrue(response.getTimestamp() > 0);
    }

    @Test
    public void testApiResponseStaticMethods() {
        // 测试静态工厂方法
        ApiResponse<String> successResponse = ApiResponse.success("成功数据");
        assertEquals(200, successResponse.getCode());
        assertEquals("操作成功", successResponse.getMessage());
        assertEquals("成功数据", successResponse.getData());

        ApiResponse<Object> errorResponse = ApiResponse.error("错误信息");
        assertEquals(500, errorResponse.getCode());
        assertEquals("错误信息", errorResponse.getMessage());
        assertNull(errorResponse.getData());

        ApiResponse<Object> badRequestResponse = ApiResponse.badRequest("参数错误");
        assertEquals(400, badRequestResponse.getCode());
        assertEquals("参数错误", badRequestResponse.getMessage());
    }

    @Test
    public void testEqualsAndHashCode() {
        // 测试EqualsAndHashCode (排除时间字段)
        WordDocument doc1 = WordDocument.builder()
                .title("相同标题")
                .content("相同内容")
                .category("相同分类")
                .author("相同作者")
                .build();

        // 稍微延迟以确保时间不同
        try {
            Thread.sleep(1);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        WordDocument doc2 = WordDocument.builder()
                .title("相同标题")
                .content("相同内容")
                .category("相同分类")
                .author("相同作者")
                .build();

        // 由于排除了时间字段，对象应该相等
        assertEquals(doc1, doc2);
        assertEquals(doc1.hashCode(), doc2.hashCode());

        // 测试不同的对象
        WordDocument doc3 = WordDocument.builder()
                .title("不同标题")
                .content("相同内容")
                .category("相同分类")
                .author("相同作者")
                .build();

        assertNotEquals(doc1, doc3);
        assertNotEquals(doc1.hashCode(), doc3.hashCode());
    }

    @Test
    public void testConvenienceConstructor() {
        // 测试便捷构造函数
        WordDocument document = new WordDocument("标题", "内容", "分类");
        
        assertEquals("标题", document.getTitle());
        assertEquals("内容", document.getContent());
        assertEquals("分类", document.getCategory());
        assertNotNull(document.getCreateTime());
        assertNotNull(document.getUpdateTime());
        assertEquals(Integer.valueOf(0), document.getViewCount());
    }
}
