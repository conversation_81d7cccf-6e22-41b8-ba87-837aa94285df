package com.howbuy.ai.search;

import static org.junit.jupiter.api.Assertions.*;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchAllQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

/** 高级搜索查询构建器测试 */
public class AdvancedSearchQueryBuilderTest {

  private AdvancedSearchQueryBuilder queryBuilder;

  @BeforeEach
  void setUp() {
    queryBuilder = new AdvancedSearchQueryBuilder();
  }

  @Test
  void testBuildAdvancedSearchQuery_NormalCase() {
    // 正常情况测试
    QueryBuilder query = queryBuilder.buildAdvancedSearchQuery("华夏成长1号");

    assertNotNull(query);
    assertTrue(query instanceof BoolQueryBuilder);

    BoolQueryBuilder boolQuery = (BoolQueryBuilder) query;
    assertFalse(boolQuery.must().isEmpty());
  }

  @Test
  void testBuildAdvancedSearchQuery_EmptyAndNull() {
    // 空字符串测试
    QueryBuilder emptyQuery = queryBuilder.buildAdvancedSearchQuery("");
    assertTrue(emptyQuery instanceof MatchAllQueryBuilder);

    // null测试
    QueryBuilder nullQuery = queryBuilder.buildAdvancedSearchQuery(null);
    assertTrue(nullQuery instanceof MatchAllQueryBuilder);

    // 空白字符串测试
    QueryBuilder blankQuery = queryBuilder.buildAdvancedSearchQuery("   ");
    assertTrue(blankQuery instanceof MatchAllQueryBuilder);
  }

  @Test
  void testBuildAdvancedSearchQuery_WithSlop() {
    // 带slop参数的测试
    QueryBuilder query1 = queryBuilder.buildAdvancedSearchQuery("华夏成长", 1);
    QueryBuilder query2 = queryBuilder.buildAdvancedSearchQuery("华夏成长", 5);

    assertNotNull(query1);
    assertNotNull(query2);
    assertTrue(query1 instanceof BoolQueryBuilder);
    assertTrue(query2 instanceof BoolQueryBuilder);
  }

  @ParameterizedTest
  @ValueSource(strings = {"华夏成长", "华夏成长1号", "000001", "招商银行2023", "基金123号", "A股指数500"})
  void testBuildAdvancedSearchQuery_VariousInputs(String keyword) {
    QueryBuilder query = queryBuilder.buildAdvancedSearchQuery(keyword);

    assertNotNull(query);
    assertTrue(query instanceof BoolQueryBuilder);
  }

  @Test
  void testBuildAdvancedSearchQuery_NumberConversion() {
    // 测试数字转换功能
    QueryBuilder query1 = queryBuilder.buildAdvancedSearchQuery("华夏1号");
    QueryBuilder query2 = queryBuilder.buildAdvancedSearchQuery("基金123");
    QueryBuilder query3 = queryBuilder.buildAdvancedSearchQuery("2023年报");

    assertNotNull(query1);
    assertNotNull(query2);
    assertNotNull(query3);

    // 所有查询都应该是BoolQueryBuilder
    assertTrue(query1 instanceof BoolQueryBuilder);
    assertTrue(query2 instanceof BoolQueryBuilder);
    assertTrue(query3 instanceof BoolQueryBuilder);
  }

  @Test
  void testBuildAdvancedSearchQueryWithFilters() {
    // 测试带过滤条件的查询
    QueryBuilder filterQuery =
        new BoolQueryBuilder()
            .filter(org.elasticsearch.index.query.QueryBuilders.termQuery("tsType", "基金"));

    QueryBuilder query = queryBuilder.buildAdvancedSearchQueryWithFilters("华夏成长", filterQuery);

    assertNotNull(query);
    assertTrue(query instanceof BoolQueryBuilder);

    BoolQueryBuilder boolQuery = (BoolQueryBuilder) query;
    assertFalse(boolQuery.must().isEmpty());
    assertFalse(boolQuery.filter().isEmpty());
  }

  @Test
  void testBuildAdvancedSearchQueryWithFilters_NullFilter() {
    // 测试null过滤条件
    QueryBuilder query = queryBuilder.buildAdvancedSearchQueryWithFilters("华夏成长", null);

    assertNotNull(query);
    assertTrue(query instanceof BoolQueryBuilder);
  }

  @Test
  void testBuildFuzzySearchQuery() {
    // 测试模糊搜索
    QueryBuilder fuzzyQuery = queryBuilder.buildFuzzySearchQuery("华夏成长");

    assertNotNull(fuzzyQuery);
    // 模糊搜索应该返回MultiMatchQuery，但由于类型检查复杂，这里只验证非null
  }

  @Test
  void testBuildFuzzySearchQuery_EmptyInput() {
    // 测试空输入的模糊搜索
    QueryBuilder emptyFuzzyQuery = queryBuilder.buildFuzzySearchQuery("");
    assertTrue(emptyFuzzyQuery instanceof MatchAllQueryBuilder);

    QueryBuilder nullFuzzyQuery = queryBuilder.buildFuzzySearchQuery(null);
    assertTrue(nullFuzzyQuery instanceof MatchAllQueryBuilder);
  }

  @Test
  void testQueryBuilderStructure() {
    // 测试查询结构
    QueryBuilder query = queryBuilder.buildAdvancedSearchQuery("华夏成长1号");

    assertTrue(query instanceof BoolQueryBuilder);
    BoolQueryBuilder boolQuery = (BoolQueryBuilder) query;

    // 应该有must条件
    assertFalse(boolQuery.must().isEmpty());

    // must条件中应该包含内容查询
    QueryBuilder mustQuery = boolQuery.must().get(0);
    assertTrue(mustQuery instanceof BoolQueryBuilder);

    BoolQueryBuilder contentQuery = (BoolQueryBuilder) mustQuery;
    // 内容查询应该有should条件
    assertFalse(contentQuery.should().isEmpty());
  }

  @Test
  void testSpecialCharacters() {
    // 测试特殊字符处理
    QueryBuilder query1 = queryBuilder.buildAdvancedSearchQuery("华夏-成长");
    QueryBuilder query2 = queryBuilder.buildAdvancedSearchQuery("华夏(成长)");
    QueryBuilder query3 = queryBuilder.buildAdvancedSearchQuery("华夏&成长");

    assertNotNull(query1);
    assertNotNull(query2);
    assertNotNull(query3);
  }

  @Test
  void testLongKeyword() {
    // 测试长关键词
    String longKeyword = "华夏成长混合型证券投资基金2023年年度报告摘要";
    QueryBuilder query = queryBuilder.buildAdvancedSearchQuery(longKeyword);

    assertNotNull(query);
    assertTrue(query instanceof BoolQueryBuilder);
  }

  @Test
  void testNumericOnlyKeyword() {
    // 测试纯数字关键词
    QueryBuilder query1 = queryBuilder.buildAdvancedSearchQuery("123");
    QueryBuilder query2 = queryBuilder.buildAdvancedSearchQuery("000001");
    QueryBuilder query3 = queryBuilder.buildAdvancedSearchQuery("2023");

    assertNotNull(query1);
    assertNotNull(query2);
    assertNotNull(query3);

    assertTrue(query1 instanceof BoolQueryBuilder);
    assertTrue(query2 instanceof BoolQueryBuilder);
    assertTrue(query3 instanceof BoolQueryBuilder);
  }

  @Test
  void testErrorHandling() {
    // 测试错误处理 - 这里主要测试不会抛出异常
    assertDoesNotThrow(
        () -> {
          queryBuilder.buildAdvancedSearchQuery("华夏成长1号");
          queryBuilder.buildAdvancedSearchQuery("");
          queryBuilder.buildAdvancedSearchQuery(null);
          queryBuilder.buildFuzzySearchQuery("测试");
        });
  }

  @Test
  void testPerformance() {
    // 性能测试
    String testKeyword = "华夏成长1号混合型基金";

    long startTime = System.currentTimeMillis();
    for (int i = 0; i < 100; i++) {
      queryBuilder.buildAdvancedSearchQuery(testKeyword);
    }
    long endTime = System.currentTimeMillis();

    // 100次查询构建应该在合理时间内完成
    assertTrue(endTime - startTime < 1000, "查询构建性能测试失败");
  }

  @Test
  void testDifferentSlops() {
    // 测试不同的slop值
    QueryBuilder query0 = queryBuilder.buildAdvancedSearchQuery("华夏成长", 0);
    QueryBuilder query3 = queryBuilder.buildAdvancedSearchQuery("华夏成长", 3);
    QueryBuilder query10 = queryBuilder.buildAdvancedSearchQuery("华夏成长", 10);

    assertNotNull(query0);
    assertNotNull(query3);
    assertNotNull(query10);

    // 所有查询都应该是有效的
    assertTrue(query0 instanceof BoolQueryBuilder);
    assertTrue(query3 instanceof BoolQueryBuilder);
    assertTrue(query10 instanceof BoolQueryBuilder);
  }
}
