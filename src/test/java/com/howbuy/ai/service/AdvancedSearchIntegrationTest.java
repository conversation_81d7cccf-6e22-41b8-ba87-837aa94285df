package com.howbuy.ai.service;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.howbuy.ai.entity.FinancialProduct;
import com.howbuy.ai.search.AdvancedSearchQueryBuilder;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.SearchHitsImpl;
import org.springframework.data.elasticsearch.core.TotalHitsRelation;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;

/** 高级搜索集成测试 */
@ExtendWith(MockitoExtension.class)
public class AdvancedSearchIntegrationTest {

  @Mock private ElasticsearchRestTemplate elasticsearchTemplate;

  @InjectMocks private AdvancedSearchQueryBuilder advancedSearchQueryBuilder;

  @InjectMocks private FinancialProductService financialProductService;

  private List<FinancialProduct> testProducts;

  @BeforeEach
  void setUp() {
    // 创建测试数据
    testProducts =
        Arrays.asList(
            FinancialProduct.builder()
                .tscode("000001")
                .tsname("华夏成长混合")
                .searchName("华夏成长混合")
                .tsType("基金")
                .weight(100L)
                .build(),
            FinancialProduct.builder()
                .tscode("000002")
                .tsname("华夏成长1号")
                .searchName("华夏成长1号")
                .tsType("基金")
                .weight(90L)
                .build(),
            FinancialProduct.builder()
                .tscode("110011")
                .tsname("招商银行2023年报")
                .searchName("招商银行2023年报")
                .tsType("股票")
                .weight(80L)
                .build());

    // 注入依赖 - 使用反射设置private字段
    try {
      java.lang.reflect.Field field =
          FinancialProductService.class.getDeclaredField("advancedSearchQueryBuilder");
      field.setAccessible(true);
      field.set(financialProductService, advancedSearchQueryBuilder);
    } catch (Exception e) {
      throw new RuntimeException("Failed to inject advancedSearchQueryBuilder", e);
    }
  }

  @Test
  void testAdvancedSearch_WithNumberConversion() {
    // 模拟Elasticsearch返回结果
    SearchHits<FinancialProduct> searchHits = createMockSearchHits(testProducts);
    when(elasticsearchTemplate.search(any(NativeSearchQuery.class), eq(FinancialProduct.class)))
        .thenReturn(searchHits);

    // 执行搜索
    Page<FinancialProduct> result = financialProductService.advancedSearch("华夏1号", 0, 10);

    // 验证结果
    assertNotNull(result);
    assertEquals(3, result.getTotalElements());
    assertFalse(result.getContent().isEmpty());
  }

  @Test
  void testAdvancedSearch_WithMultipleNumbers() {
    // 测试包含多个数字的搜索
    SearchHits<FinancialProduct> searchHits =
        createMockSearchHits(Collections.singletonList(testProducts.get(2)));
    when(elasticsearchTemplate.search(any(NativeSearchQuery.class), eq(FinancialProduct.class)))
        .thenReturn(searchHits);

    Page<FinancialProduct> result = financialProductService.advancedSearch("招商银行2023", 0, 10);

    assertNotNull(result);
    assertEquals(1, result.getTotalElements());
  }

  @Test
  void testAdvancedSearch_NoNumbers() {
    // 测试不包含数字的搜索
    SearchHits<FinancialProduct> searchHits =
        createMockSearchHits(Collections.singletonList(testProducts.get(0)));
    when(elasticsearchTemplate.search(any(NativeSearchQuery.class), eq(FinancialProduct.class)))
        .thenReturn(searchHits);

    Page<FinancialProduct> result = financialProductService.advancedSearch("华夏成长", 0, 10);

    assertNotNull(result);
    assertEquals(1, result.getTotalElements());
  }

  @Test
  void testAdvancedSearch_EmptyResult() {
    // 测试空结果
    SearchHits<FinancialProduct> emptySearchHits = createMockSearchHits(Collections.emptyList());
    when(elasticsearchTemplate.search(any(NativeSearchQuery.class), eq(FinancialProduct.class)))
        .thenReturn(emptySearchHits);

    Page<FinancialProduct> result = financialProductService.advancedSearch("不存在的产品", 0, 10);

    assertNotNull(result);
    assertEquals(0, result.getTotalElements());
    assertTrue(result.getContent().isEmpty());
  }

  @Test
  void testAdvancedSearch_WithCustomSlop() {
    // 测试自定义slop值
    SearchHits<FinancialProduct> searchHits = createMockSearchHits(testProducts);
    when(elasticsearchTemplate.search(any(NativeSearchQuery.class), eq(FinancialProduct.class)))
        .thenReturn(searchHits);

    Page<FinancialProduct> result = financialProductService.advancedSearch("华夏成长", 0, 10, 5);

    assertNotNull(result);
    assertEquals(3, result.getTotalElements());
  }

  @Test
  void testAdvancedSearch_Pagination() {
    // 测试分页
    SearchHits<FinancialProduct> searchHits = createMockSearchHits(testProducts.subList(0, 2));
    when(elasticsearchTemplate.search(any(NativeSearchQuery.class), eq(FinancialProduct.class)))
        .thenReturn(searchHits);

    Page<FinancialProduct> result = financialProductService.advancedSearch("华夏", 0, 2);

    assertNotNull(result);
    assertEquals(2, result.getTotalElements());
    assertEquals(2, result.getContent().size());
    assertEquals(0, result.getNumber()); // 第一页
    assertEquals(2, result.getSize());
  }

  @Test
  void testAdvancedSearch_ErrorHandling() {
    // 测试错误处理
    when(elasticsearchTemplate.search(any(NativeSearchQuery.class), eq(FinancialProduct.class)))
        .thenThrow(new RuntimeException("Elasticsearch error"));

    assertThrows(
        RuntimeException.class,
        () -> {
          financialProductService.advancedSearch("华夏成长", 0, 10);
        });
  }

  @Test
  void testAdvancedSearch_SpecialCharacters() {
    // 测试特殊字符
    SearchHits<FinancialProduct> searchHits = createMockSearchHits(testProducts);
    when(elasticsearchTemplate.search(any(NativeSearchQuery.class), eq(FinancialProduct.class)))
        .thenReturn(searchHits);

    assertDoesNotThrow(
        () -> {
          financialProductService.advancedSearch("华夏-成长", 0, 10);
          financialProductService.advancedSearch("华夏(成长)", 0, 10);
          financialProductService.advancedSearch("华夏&成长", 0, 10);
        });
  }

  @Test
  void testAdvancedSearch_LongKeyword() {
    // 测试长关键词
    String longKeyword = "华夏成长混合型证券投资基金2023年年度报告摘要第1期";
    SearchHits<FinancialProduct> searchHits = createMockSearchHits(testProducts);
    when(elasticsearchTemplate.search(any(NativeSearchQuery.class), eq(FinancialProduct.class)))
        .thenReturn(searchHits);

    assertDoesNotThrow(
        () -> {
          Page<FinancialProduct> result =
              financialProductService.advancedSearch(longKeyword, 0, 10);
          assertNotNull(result);
        });
  }

  @Test
  void testAdvancedSearch_NumericKeyword() {
    // 测试纯数字关键词
    SearchHits<FinancialProduct> searchHits = createMockSearchHits(testProducts);
    when(elasticsearchTemplate.search(any(NativeSearchQuery.class), eq(FinancialProduct.class)))
        .thenReturn(searchHits);

    assertDoesNotThrow(
        () -> {
          Page<FinancialProduct> result1 = financialProductService.advancedSearch("123", 0, 10);
          Page<FinancialProduct> result2 = financialProductService.advancedSearch("000001", 0, 10);
          Page<FinancialProduct> result3 = financialProductService.advancedSearch("2023", 0, 10);

          assertNotNull(result1);
          assertNotNull(result2);
          assertNotNull(result3);
        });
  }

  @Test
  void testAdvancedSearch_Performance() {
    // 性能测试
    SearchHits<FinancialProduct> searchHits = createMockSearchHits(testProducts);
    when(elasticsearchTemplate.search(any(NativeSearchQuery.class), eq(FinancialProduct.class)))
        .thenReturn(searchHits);

    long startTime = System.currentTimeMillis();
    for (int i = 0; i < 10; i++) {
      financialProductService.advancedSearch("华夏成长" + i, 0, 10);
    }
    long endTime = System.currentTimeMillis();

    // 10次搜索应该在合理时间内完成
    assertTrue(endTime - startTime < 5000, "高级搜索性能测试失败");
  }

  /** 创建模拟的SearchHits对象 */
  private SearchHits<FinancialProduct> createMockSearchHits(List<FinancialProduct> products) {
    List<SearchHit<FinancialProduct>> searchHitList =
        products.stream()
            .map(
                product -> {
                  SearchHit<FinancialProduct> hit =
                      new SearchHit<>(
                          "ai_main_search",
                          product.getTscode(),
                          null,
                          1.0f,
                          null,
                          null,
                          null,
                          null,
                          null,
                          null,
                          product);
                  return hit;
                })
            .collect(java.util.stream.Collectors.toList());

    return new SearchHitsImpl<>(
        products.size(), TotalHitsRelation.EQUAL_TO, 1.0f, null, searchHitList, null, null);
  }
}
