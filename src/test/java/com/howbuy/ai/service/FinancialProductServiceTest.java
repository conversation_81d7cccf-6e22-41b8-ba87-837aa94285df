package com.howbuy.ai.service;

import com.howbuy.ai.dto.FinancialProductSearchRequest;
import com.howbuy.ai.entity.FinancialProduct;
import com.howbuy.ai.repository.FinancialProductRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 金融产品服务测试类
 */
@ExtendWith(MockitoExtension.class)
public class FinancialProductServiceTest {

    @Mock
    private FinancialProductRepository repository;

    @Mock
    private ElasticsearchRestTemplate elasticsearchTemplate;

    @InjectMocks
    private FinancialProductService service;

    private FinancialProduct testProduct;
    private List<FinancialProduct> testProducts;

    @BeforeEach
    void setUp() {
        testProduct = FinancialProduct.builder()
                .tscode("000001")
                .tsname("测试基金")
                .tsshortName("测试基金")
                .searchName("测试基金")
                .searchShortName("测试基金")
                .tsType("基金")
                .businessType("公募基金")
                .attribute("股票型")
                .opBuyStatus(1L)
                .opRedeemStatus(1L)
                .xsbz(1L)
                .zsbz(1L)
                .weight(100L)
                .build();

        FinancialProduct product2 = FinancialProduct.builder()
                .tscode("000002")
                .tsname("另一个基金")
                .tsshortName("另一个基金")
                .searchName("另一个基金")
                .searchShortName("另一个基金")
                .tsType("基金")
                .businessType("公募基金")
                .attribute("债券型")
                .opBuyStatus(1L)
                .opRedeemStatus(0L)
                .xsbz(1L)
                .zsbz(1L)
                .weight(80L)
                .build();

        testProducts = Arrays.asList(testProduct, product2);
    }

    @Test
    void testFindByTscode() {
        // Given
        when(repository.findByTscode("000001")).thenReturn(testProduct);

        // When
        Optional<FinancialProduct> result = service.findByTscode("000001");

        // Then
        assertTrue(result.isPresent());
        assertEquals("000001", result.get().getTscode());
        assertEquals("测试基金", result.get().getTsname());
        verify(repository).findByTscode("000001");
    }

    @Test
    void testFindByTscodeNotFound() {
        // Given
        when(repository.findByTscode("999999")).thenReturn(null);

        // When
        Optional<FinancialProduct> result = service.findByTscode("999999");

        // Then
        assertFalse(result.isPresent());
        verify(repository).findByTscode("999999");
    }

    @Test
    void testSearchWithKeyword() {
        // Given
        FinancialProductSearchRequest request = FinancialProductSearchRequest.builder()
                .keyword("测试")
                .searchType(FinancialProductSearchRequest.SearchType.COMPREHENSIVE)
                .page(0)
                .size(20)
                .build();

        Pageable pageable = PageRequest.of(0, 20);
        Page<FinancialProduct> expectedPage = new PageImpl<>(testProducts, pageable, testProducts.size());
        
        when(repository.comprehensiveSearch(eq("测试"), any(Pageable.class)))
                .thenReturn(expectedPage);

        // When
        Page<FinancialProduct> result = service.search(request);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getTotalElements());
        assertEquals(testProducts.size(), result.getContent().size());
        verify(repository).comprehensiveSearch(eq("测试"), any(Pageable.class));
    }

    @Test
    void testSearchWithTscode() {
        // Given
        FinancialProductSearchRequest request = FinancialProductSearchRequest.builder()
                .tscode("000001")
                .searchType(FinancialProductSearchRequest.SearchType.EXACT)
                .page(0)
                .size(20)
                .build();

        when(repository.findByTscode("000001")).thenReturn(testProduct);

        // When
        Page<FinancialProduct> result = service.search(request);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals("000001", result.getContent().get(0).getTscode());
        verify(repository).findByTscode("000001");
    }

    @Test
    void testSearchPinyin() {
        // Given
        FinancialProductSearchRequest request = FinancialProductSearchRequest.builder()
                .keyword("ceshi")
                .searchType(FinancialProductSearchRequest.SearchType.PINYIN)
                .page(0)
                .size(20)
                .build();

        Pageable pageable = PageRequest.of(0, 20);
        Page<FinancialProduct> expectedPage = new PageImpl<>(Collections.singletonList(testProduct), pageable, 1);
        
        when(repository.pinyinSearch(eq("ceshi"), any(Pageable.class)))
                .thenReturn(expectedPage);

        // When
        Page<FinancialProduct> result = service.search(request);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        verify(repository).pinyinSearch(eq("ceshi"), any(Pageable.class));
    }

    @Test
    void testSearchPrefix() {
        // Given
        FinancialProductSearchRequest request = FinancialProductSearchRequest.builder()
                .keyword("0000")
                .searchType(FinancialProductSearchRequest.SearchType.PREFIX)
                .page(0)
                .size(20)
                .build();

        Pageable pageable = PageRequest.of(0, 20);
        Page<FinancialProduct> expectedPage = new PageImpl<>(testProducts, pageable, testProducts.size());
        
        when(repository.findByTscodePrefix(eq("0000"), any(Pageable.class)))
                .thenReturn(expectedPage);

        // When
        Page<FinancialProduct> result = service.search(request);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getTotalElements());
        verify(repository).findByTscodePrefix(eq("0000"), any(Pageable.class));
    }

    @Test
    void testGetBuyableProducts() {
        // Given
        Pageable pageable = PageRequest.of(0, 20);
        Page<FinancialProduct> expectedPage = new PageImpl<>(Collections.singletonList(testProduct), pageable, 1);
        
        when(repository.findBuyableProducts(any(Pageable.class)))
                .thenReturn(expectedPage);

        // When
        Page<FinancialProduct> result = service.getBuyableProducts(0, 20);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertTrue(result.getContent().get(0).isBuyable());
        verify(repository).findBuyableProducts(any(Pageable.class));
    }

    @Test
    void testGetRedeemableProducts() {
        // Given
        Pageable pageable = PageRequest.of(0, 20);
        Page<FinancialProduct> expectedPage = new PageImpl<>(Collections.singletonList(testProduct), pageable, 1);
        
        when(repository.findRedeemableProducts(any(Pageable.class)))
                .thenReturn(expectedPage);

        // When
        Page<FinancialProduct> result = service.getRedeemableProducts(0, 20);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertTrue(result.getContent().get(0).isRedeemable());
        verify(repository).findRedeemableProducts(any(Pageable.class));
    }

    @Test
    void testGetPopularProducts() {
        // Given
        Pageable pageable = PageRequest.of(0, 20);
        Page<FinancialProduct> expectedPage = new PageImpl<>(testProducts, pageable, testProducts.size());
        
        when(repository.findPopularProducts(any(Pageable.class)))
                .thenReturn(expectedPage);

        // When
        Page<FinancialProduct> result = service.getPopularProducts(0, 20);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getTotalElements());
        verify(repository).findPopularProducts(any(Pageable.class));
    }

    @Test
    void testGetProductsByType() {
        // Given
        String tsType = "基金";
        Pageable pageable = PageRequest.of(0, 20);
        Page<FinancialProduct> expectedPage = new PageImpl<>(testProducts, pageable, testProducts.size());
        
        when(repository.findByTsType(eq(tsType), any(Pageable.class)))
                .thenReturn(expectedPage);

        // When
        Page<FinancialProduct> result = service.getProductsByType(tsType, 0, 20);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getTotalElements());
        result.getContent().forEach(product -> assertEquals(tsType, product.getTsType()));
        verify(repository).findByTsType(eq(tsType), any(Pageable.class));
    }

    @Test
    void testProductStatusMethods() {
        // Test buyable product
        assertTrue(testProduct.isBuyable());
        assertTrue(testProduct.isRedeemable());
        assertTrue(testProduct.isOnSale());
        assertTrue(testProduct.isDisplayed());

        // Test non-buyable product
        FinancialProduct nonBuyableProduct = FinancialProduct.builder()
                .tscode("000003")
                .opBuyStatus(0L)
                .opRedeemStatus(0L)
                .xsbz(0L)
                .zsbz(0L)
                .build();

        assertFalse(nonBuyableProduct.isBuyable());
        assertFalse(nonBuyableProduct.isRedeemable());
        assertFalse(nonBuyableProduct.isOnSale());
        assertFalse(nonBuyableProduct.isDisplayed());
    }
}
