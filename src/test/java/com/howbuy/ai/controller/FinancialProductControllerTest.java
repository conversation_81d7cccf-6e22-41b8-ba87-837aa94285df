package com.howbuy.ai.controller;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.howbuy.ai.dto.FinancialProductSearchRequest;
import com.howbuy.ai.entity.FinancialProduct;
import com.howbuy.ai.service.FinancialProductService;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

/** 金融产品控制器测试类 */
@WebMvcTest(FinancialProductController.class)
public class FinancialProductControllerTest {

  @Autowired private MockMvc mockMvc;

  @MockBean private FinancialProductService financialProductService;

  @Autowired private ObjectMapper objectMapper;

  private FinancialProduct testProduct;
  private List<FinancialProduct> testProducts;

  @BeforeEach
  void setUp() {
    testProduct =
        FinancialProduct.builder()
            .tscode("000001")
            .tsname("测试基金")
            .tsshortName("测试基金")
            .searchName("测试基金")
            .searchShortName("测试基金")
            .tsType("基金")
            .businessType("公募基金")
            .attribute("股票型")
            .opBuyStatus(1L)
            .opRedeemStatus(1L)
            .xsbz(1L)
            .zsbz(1L)
            .weight(100L)
            .build();

    FinancialProduct product2 =
        FinancialProduct.builder()
            .tscode("000002")
            .tsname("另一个基金")
            .tsshortName("另一个基金")
            .searchName("另一个基金")
            .searchShortName("另一个基金")
            .tsType("基金")
            .businessType("公募基金")
            .attribute("债券型")
            .opBuyStatus(1L)
            .opRedeemStatus(0L)
            .xsbz(1L)
            .zsbz(1L)
            .weight(80L)
            .build();

    testProducts = Arrays.asList(testProduct, product2);
  }

  @Test
  void testGetProductByTscode() throws Exception {
    // Given
    when(financialProductService.findByTscode("000001")).thenReturn(Optional.of(testProduct));

    // When & Then
    mockMvc
        .perform(get("/api/v1/financial-products/000001"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.code").value(200))
        .andExpect(jsonPath("$.message").value("获取成功"))
        .andExpect(jsonPath("$.data.tscode").value("000001"))
        .andExpect(jsonPath("$.data.tsname").value("测试基金"));
  }

  @Test
  void testGetProductByTscodeNotFound() throws Exception {
    // Given
    when(financialProductService.findByTscode("999999")).thenReturn(Optional.empty());

    // When & Then
    mockMvc.perform(get("/api/v1/financial-products/999999")).andExpect(status().isNotFound());
  }

  @Test
  void testSearchWithPost() throws Exception {
    // Given
    FinancialProductSearchRequest request =
        FinancialProductSearchRequest.builder()
            .keyword("测试")
            .searchType(FinancialProductSearchRequest.SearchType.COMPREHENSIVE)
            .page(0)
            .size(20)
            .build();

    Page<FinancialProduct> resultPage =
        new PageImpl<>(testProducts, PageRequest.of(0, 20), testProducts.size());

    when(financialProductService.search(any(FinancialProductSearchRequest.class)))
        .thenReturn(resultPage);

    // When & Then
    mockMvc
        .perform(
            post("/api/v1/financial-products/search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.code").value(200))
        .andExpect(jsonPath("$.message").value("搜索成功"))
        .andExpect(jsonPath("$.data.totalElements").value(2))
        .andExpect(jsonPath("$.data.content[0].tscode").value("000001"))
        .andExpect(jsonPath("$.data.content[1].tscode").value("000002"));
  }

  @Test
  void testSimpleSearch() throws Exception {
    // Given
    Page<FinancialProduct> resultPage =
        new PageImpl<>(Collections.singletonList(testProduct), PageRequest.of(0, 20), 1);

    when(financialProductService.search(any(FinancialProductSearchRequest.class)))
        .thenReturn(resultPage);

    // When & Then
    mockMvc
        .perform(
            get("/api/v1/financial-products/search")
                .param("keyword", "测试")
                .param("searchType", "COMPREHENSIVE")
                .param("page", "0")
                .param("size", "20"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.code").value(200))
        .andExpect(jsonPath("$.message").value("搜索成功"))
        .andExpect(jsonPath("$.data.totalElements").value(1))
        .andExpect(jsonPath("$.data.content[0].tscode").value("000001"));
  }

  @Test
  void testGetBuyableProducts() throws Exception {
    // Given
    Page<FinancialProduct> resultPage =
        new PageImpl<>(Collections.singletonList(testProduct), PageRequest.of(0, 20), 1);

    when(financialProductService.getBuyableProducts(0, 20)).thenReturn(resultPage);

    // When & Then
    mockMvc
        .perform(get("/api/v1/financial-products/buyable").param("page", "0").param("size", "20"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.code").value(200))
        .andExpect(jsonPath("$.message").value("获取成功"))
        .andExpect(jsonPath("$.data.totalElements").value(1))
        .andExpect(jsonPath("$.data.content[0].tscode").value("000001"));
  }

  @Test
  void testGetRedeemableProducts() throws Exception {
    // Given
    Page<FinancialProduct> resultPage =
        new PageImpl<>(Collections.singletonList(testProduct), PageRequest.of(0, 20), 1);

    when(financialProductService.getRedeemableProducts(0, 20)).thenReturn(resultPage);

    // When & Then
    mockMvc
        .perform(
            get("/api/v1/financial-products/redeemable").param("page", "0").param("size", "20"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.code").value(200))
        .andExpect(jsonPath("$.message").value("获取成功"))
        .andExpect(jsonPath("$.data.totalElements").value(1));
  }

  @Test
  void testGetPopularProducts() throws Exception {
    // Given
    Page<FinancialProduct> resultPage =
        new PageImpl<>(testProducts, PageRequest.of(0, 20), testProducts.size());

    when(financialProductService.getPopularProducts(0, 20)).thenReturn(resultPage);

    // When & Then
    mockMvc
        .perform(get("/api/v1/financial-products/popular").param("page", "0").param("size", "20"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.code").value(200))
        .andExpect(jsonPath("$.message").value("获取成功"))
        .andExpect(jsonPath("$.data.totalElements").value(2));
  }

  @Test
  void testGetProductsByType() throws Exception {
    // Given
    Page<FinancialProduct> resultPage =
        new PageImpl<>(testProducts, PageRequest.of(0, 20), testProducts.size());

    when(financialProductService.getProductsByType("基金", 0, 20)).thenReturn(resultPage);

    // When & Then
    mockMvc
        .perform(get("/api/v1/financial-products/type/基金").param("page", "0").param("size", "20"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.code").value(200))
        .andExpect(jsonPath("$.message").value("获取成功"))
        .andExpect(jsonPath("$.data.totalElements").value(2));
  }

  @Test
  void testPinyinSearch() throws Exception {
    // Given
    Page<FinancialProduct> resultPage =
        new PageImpl<>(Collections.singletonList(testProduct), PageRequest.of(0, 20), 1);

    when(financialProductService.search(any(FinancialProductSearchRequest.class)))
        .thenReturn(resultPage);

    // When & Then
    mockMvc
        .perform(
            get("/api/v1/financial-products/search/pinyin")
                .param("keyword", "ceshi")
                .param("page", "0")
                .param("size", "20"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.code").value(200))
        .andExpect(jsonPath("$.message").value("拼音搜索成功"));
  }

  @Test
  void testPrefixSearch() throws Exception {
    // Given
    Page<FinancialProduct> resultPage =
        new PageImpl<>(testProducts, PageRequest.of(0, 20), testProducts.size());

    when(financialProductService.search(any(FinancialProductSearchRequest.class)))
        .thenReturn(resultPage);

    // When & Then
    mockMvc
        .perform(
            get("/api/v1/financial-products/search/prefix")
                .param("prefix", "0000")
                .param("page", "0")
                .param("size", "20"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.code").value(200))
        .andExpect(jsonPath("$.message").value("前缀搜索成功"));
  }

  @Test
  void testSearchWithEmptyKeyword() throws Exception {
    // When & Then
    mockMvc
        .perform(get("/api/v1/financial-products/search").param("keyword", ""))
        .andExpect(status().isBadRequest());
  }

  @Test
  void testGetProductWithEmptyTscode() throws Exception {
    // When & Then
    mockMvc.perform(get("/api/v1/financial-products/")).andExpect(status().isNotFound());
  }
}
