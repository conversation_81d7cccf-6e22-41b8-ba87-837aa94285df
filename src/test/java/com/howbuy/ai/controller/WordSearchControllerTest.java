package com.howbuy.ai.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.howbuy.ai.dto.SearchRequest;
import com.howbuy.ai.entity.WordDocument;
import com.howbuy.ai.service.WordSearchService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * WordSearchController测试类
 */
@WebMvcTest(WordSearchController.class)
class WordSearchControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private WordSearchService wordSearchService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private WordDocument testDocument;
    
    @BeforeEach
    void setUp() {
        testDocument = new WordDocument();
        testDocument.setId("1");
        testDocument.setTitle("测试文档");
        testDocument.setContent("这是一个测试文档的内容");
        testDocument.setCategory("测试");
        testDocument.setAuthor("测试作者");
    }
    
    @Test
    void testSearch() throws Exception {
        // 模拟搜索结果
        Page<WordDocument> mockPage = new PageImpl<>(Arrays.asList(testDocument));
        when(wordSearchService.searchByKeyword(anyString(), anyInt(), anyInt()))
                .thenReturn(mockPage);
        
        mockMvc.perform(get("/v1/search")
                .param("keyword", "测试")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("搜索成功"))
                .andExpect(jsonPath("$.data.content[0].title").value("测试文档"));
    }
    
    @Test
    void testAdvancedSearch() throws Exception {
        // 创建搜索请求
        SearchRequest request = new SearchRequest();
        request.setKeyword("测试");
        request.setPage(0);
        request.setSize(10);
        
        // 模拟搜索结果
        Page<WordDocument> mockPage = new PageImpl<>(Arrays.asList(testDocument));
        when(wordSearchService.searchByKeyword(anyString(), anyInt(), anyInt()))
                .thenReturn(mockPage);
        
        mockMvc.perform(post("/v1/search/advanced")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("高级搜索成功"));
    }
    
    @Test
    void testGetDocument() throws Exception {
        // 模拟查找文档
        when(wordSearchService.findById("1")).thenReturn(Optional.of(testDocument));
        
        mockMvc.perform(get("/v1/search/document/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value("测试文档"));
    }
    
    @Test
    void testGetDocumentNotFound() throws Exception {
        // 模拟文档不存在
        when(wordSearchService.findById("999")).thenReturn(Optional.empty());
        
        mockMvc.perform(get("/v1/search/document/999"))
                .andExpect(status().isNotFound());
    }
    
    @Test
    void testSearchWithEmptyKeyword() throws Exception {
        mockMvc.perform(get("/v1/search")
                .param("keyword", "")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isBadRequest());
    }
}
