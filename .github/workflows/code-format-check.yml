name: Code Format Check

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  format-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      
    - name: Set up JDK 8
      uses: actions/setup-java@v3
      with:
        java-version: '8'
        distribution: 'temurin'
        
    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
        
    - name: Check code format with Maven
      run: mvn fmt:check
      
    - name: Run Checkstyle
      run: mvn checkstyle:check
      continue-on-error: true
      
    - name: Upload Checkstyle results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: checkstyle-results
        path: target/checkstyle-result.xml
        
  format-with-google-java-format:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Set up JDK 8
      uses: actions/setup-java@v3
      with:
        java-version: '8'
        distribution: 'temurin'
        
    - name: Download Google Java Format
      run: |
        wget https://github.com/google/google-java-format/releases/download/v1.15.0/google-java-format-1.15.0-all-deps.jar
        
    - name: Check format with Google Java Format
      run: |
        java -jar google-java-format-1.15.0-all-deps.jar --dry-run --set-exit-if-changed $(find src -name "*.java")
        
    - name: Format code if needed
      if: failure()
      run: |
        java -jar google-java-format-1.15.0-all-deps.jar --replace $(find src -name "*.java")
        
    - name: Commit formatted code
      if: failure()
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add .
        git diff --staged --quiet || git commit -m "Auto-format code with Google Java Format"
        git push
