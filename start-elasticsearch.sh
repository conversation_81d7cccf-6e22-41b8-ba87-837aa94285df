#!/bin/bash

# 启动Elasticsearch的脚本
echo "正在启动Elasticsearch..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装，请先安装Docker"
    exit 1
fi

# 停止并删除现有的Elasticsearch容器（如果存在）
echo "清理现有容器..."
docker stop elasticsearch 2>/dev/null || true
docker rm elasticsearch 2>/dev/null || true

# 启动新的Elasticsearch容器
echo "启动Elasticsearch容器..."
docker run -d \
  --name elasticsearch \
  -p 9200:9200 \
  -p 9300:9300 \
  -e "discovery.type=single-node" \
  -e "ES_JAVA_OPTS=-Xms512m -Xmx512m" \
  -e "xpack.security.enabled=false" \
  elasticsearch:7.17.15

# 等待Elasticsearch启动
echo "等待Elasticsearch启动..."
sleep 30

# 检查Elasticsearch是否正常运行
echo "检查Elasticsearch状态..."
if curl -s http://localhost:9200/_cluster/health | grep -q "green\|yellow"; then
    echo "✅ Elasticsearch启动成功！"
    echo "访问地址: http://localhost:9200"
    echo "集群状态: $(curl -s http://localhost:9200/_cluster/health | jq -r .status 2>/dev/null || echo "运行中")"
else
    echo "❌ Elasticsearch启动失败，请检查日志："
    docker logs elasticsearch
    exit 1
fi

echo ""
echo "现在可以启动Spring Boot应用了："
echo "mvn spring-boot:run"
