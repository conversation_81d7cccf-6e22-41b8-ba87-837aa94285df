# 金融产品搜索API指南

本文档介绍如何使用金融产品搜索API，该API基于Elasticsearch实现，支持多种搜索方式和高级查询功能。

## 索引结构

### ai_main_search索引

该索引存储金融产品信息，包含以下主要字段：

| 字段名 | 类型 | 描述 | 分析器 |
|--------|------|------|--------|
| `tscode` | text/keyword | 产品代码 | onebyone_analyzer |
| `tsname` | keyword/text | 产品名称 | onebyone_analyzer |
| `tsshortName` | keyword/text | 产品简称 | onebyone_analyzer |
| `tsshortPinyin` | text | 产品简拼 | onebyone_analyzer |
| `searchName` | text | 搜索名称 | ik_pinyin_analyzer/full_pinyin_analyzer |
| `searchShortName` | text | 搜索简称 | ik_pinyin_analyzer/full_pinyin_analyzer |
| `tsType` | keyword | 产品类型 | - |
| `businessType` | keyword | 业务类型 | - |
| `attribute` | keyword | 产品属性 | - |
| `op_buy_status` | long | 申购状态 (0-不可申购, 1-可申购) | - |
| `op_redeem_status` | long | 赎回状态 (0-不可赎回, 1-可赎回) | - |
| `xsbz` | long | 销售标志 (0-不在售, 1-在售) | - |
| `zsbz` | long | 展示标志 (0-不展示, 1-展示) | - |
| `weight` | long | 权重（用于排序） | - |
| `rtlx` | text | 日期类型 | - |

### 分析器说明

- **ik_pinyin_analyzer**: 支持中文分词和拼音搜索
- **full_pinyin_analyzer**: 全拼音分析器，支持完整拼音搜索
- **onebyone_analyzer**: 逐字符分析器，支持精确匹配

## API接口

### 基础URL
```
/api/v1/financial-products
```

### 1. 根据产品代码获取产品详情

**GET** `/api/v1/financial-products/{tscode}`

**参数:**
- `tscode`: 产品代码（路径参数）

**响应示例:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "tscode": "000001",
    "tsname": "华夏成长混合",
    "tsshortName": "华夏成长",
    "tsType": "基金",
    "businessType": "公募基金",
    "attribute": "混合型",
    "opBuyStatus": 1,
    "opRedeemStatus": 1,
    "xsbz": 1,
    "zsbz": 1,
    "weight": 100
  },
  "timestamp": 1640995200000
}
```

### 2. 综合搜索（POST）

**POST** `/api/v1/financial-products/search`

**请求体:**
```json
{
  "keyword": "华夏",
  "searchType": "COMPREHENSIVE",
  "tsTypes": ["基金"],
  "businessTypes": ["公募基金"],
  "opBuyStatus": 1,
  "enablePinyinSearch": true,
  "sortField": "WEIGHT",
  "sortDirection": "DESC",
  "page": 0,
  "size": 20
}
```

**搜索类型说明:**
- `COMPREHENSIVE`: 综合搜索（默认）
- `EXACT`: 精确匹配
- `PREFIX`: 前缀匹配
- `PINYIN`: 拼音搜索
- `FUZZY`: 模糊搜索

**排序字段说明:**
- `WEIGHT`: 按权重排序（默认）
- `TSCODE`: 按产品代码排序
- `TSNAME`: 按产品名称排序
- `SCORE`: 按相关性排序

### 3. 简单搜索（GET）

**GET** `/api/v1/financial-products/search`

**参数:**
- `keyword`: 搜索关键词（必填）
- `searchType`: 搜索类型（可选，默认COMPREHENSIVE）
- `page`: 页码（可选，默认0）
- `size`: 每页大小（可选，默认20）

**示例:**
```
GET /api/v1/financial-products/search?keyword=华夏&searchType=COMPREHENSIVE&page=0&size=10
```

### 4. 获取可申购产品

**GET** `/api/v1/financial-products/buyable`

**参数:**
- `page`: 页码（可选，默认0）
- `size`: 每页大小（可选，默认20）

### 5. 获取可赎回产品

**GET** `/api/v1/financial-products/redeemable`

**参数:**
- `page`: 页码（可选，默认0）
- `size`: 每页大小（可选，默认20）

### 6. 获取热门产品

**GET** `/api/v1/financial-products/popular`

**参数:**
- `page`: 页码（可选，默认0）
- `size`: 每页大小（可选，默认20）

### 7. 根据产品类型获取产品

**GET** `/api/v1/financial-products/type/{tsType}`

**参数:**
- `tsType`: 产品类型（路径参数）
- `page`: 页码（可选，默认0）
- `size`: 每页大小（可选，默认20）

### 8. 拼音搜索

**GET** `/api/v1/financial-products/search/pinyin`

**参数:**
- `keyword`: 拼音关键词（必填）
- `page`: 页码（可选，默认0）
- `size`: 每页大小（可选，默认20）

**示例:**
```
GET /api/v1/financial-products/search/pinyin?keyword=huaxia
```

### 9. 前缀搜索

**GET** `/api/v1/financial-products/search/prefix`

**参数:**
- `prefix`: 搜索前缀（必填）
- `page`: 页码（可选，默认0）
- `size`: 每页大小（可选，默认20）

**示例:**
```
GET /api/v1/financial-products/search/prefix?prefix=0000
```

## 搜索最佳实践

### 1. 综合搜索策略

综合搜索会同时匹配以下字段：
- 产品名称（权重3.0）
- 产品简称（权重2.0）
- 产品代码
- 拼音字段（权重2.0）

### 2. 拼音搜索支持

- 支持全拼音搜索：`huaxiachengzhang`
- 支持简拼搜索：`hxcz`
- 支持混合搜索：`huaxia成长`

### 3. 状态过滤

可以通过以下状态字段进行过滤：
- `opBuyStatus`: 申购状态
- `opRedeemStatus`: 赎回状态
- `xsbz`: 销售标志
- `zsbz`: 展示标志

### 4. 权重排序

产品按权重降序排列，权重越高的产品排名越靠前。

## 使用示例

### Java客户端示例

```java
// 1. 根据产品代码查询
Optional<FinancialProduct> product = financialProductService.findByTscode("000001");

// 2. 关键词搜索
FinancialProductSearchRequest request = FinancialProductSearchRequest.builder()
    .keyword("华夏")
    .searchType(SearchType.COMPREHENSIVE)
    .page(0)
    .size(20)
    .build();
Page<FinancialProduct> results = financialProductService.search(request);

// 3. 获取可申购产品
Page<FinancialProduct> buyableProducts = financialProductService.getBuyableProducts(0, 20);

// 4. 拼音搜索
FinancialProductSearchRequest pinyinRequest = FinancialProductSearchRequest.builder()
    .keyword("huaxia")
    .searchType(SearchType.PINYIN)
    .build();
Page<FinancialProduct> pinyinResults = financialProductService.search(pinyinRequest);
```

### cURL示例

```bash
# 1. 获取产品详情
curl -X GET "http://localhost:8080/api/v1/financial-products/000001"

# 2. 关键词搜索
curl -X GET "http://localhost:8080/api/v1/financial-products/search?keyword=华夏&page=0&size=10"

# 3. 综合搜索（POST）
curl -X POST "http://localhost:8080/api/v1/financial-products/search" \
  -H "Content-Type: application/json" \
  -d '{
    "keyword": "华夏",
    "searchType": "COMPREHENSIVE",
    "opBuyStatus": 1,
    "page": 0,
    "size": 20
  }'

# 4. 拼音搜索
curl -X GET "http://localhost:8080/api/v1/financial-products/search/pinyin?keyword=huaxia"

# 5. 获取可申购产品
curl -X GET "http://localhost:8080/api/v1/financial-products/buyable?page=0&size=10"
```

## 性能优化建议

1. **使用合适的搜索类型**: 根据业务需求选择最合适的搜索类型
2. **合理设置分页大小**: 建议每页不超过100条记录
3. **利用缓存**: 对热门搜索结果进行缓存
4. **索引优化**: 定期优化Elasticsearch索引
5. **监控性能**: 使用内置的性能监控功能

## 错误处理

API使用统一的错误响应格式：

```json
{
  "code": 400,
  "message": "搜索关键词不能为空",
  "data": null,
  "timestamp": 1640995200000
}
```

常见错误码：
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误
