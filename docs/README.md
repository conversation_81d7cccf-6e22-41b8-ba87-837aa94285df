# 📚 项目文档索引

本目录包含了金融产品搜索服务的完整技术文档。

## 📋 文档概览

### 🔍 核心功能文档

#### [金融产品搜索API指南](financial-product-search-guide.md)
- **用途**: 完整的API使用指南
- **内容**:
  - Elasticsearch索引结构说明
  - REST API接口详细说明
  - 搜索类型和参数说明
  - Java和cURL使用示例
  - 性能优化建议
- **适用人群**: 前端开发者、API集成开发者、测试人员

### ⚙️ 配置和部署文档

#### [Elasticsearch配置现代化迁移指南](elasticsearch-config-migration-guide.md)
- **用途**: Elasticsearch配置升级指南
- **内容**:
  - 从废弃API迁移到现代API
  - 类型安全的配置属性
  - 连接池和SSL配置
  - 健康检查和监控
- **适用人群**: 运维人员、后端开发者

#### [Log4j2日志配置指南](logging-guide.md)
- **用途**: 日志系统配置和使用
- **内容**:
  - 异步日志配置
  - 多种输出格式
  - 性能监控日志
  - ELK Stack集成
- **适用人群**: 运维人员、开发者

### 🎨 开发工具文档

#### [代码格式化指南](code-formatting-guide.md)
- **用途**: Google Java Format使用指南
- **内容**:
  - Maven插件配置
  - IDE集成设置
  - 脚本使用方法
  - 团队协作规范
- **适用人群**: 所有开发者

#### [格式化脚本最佳实践](format-script-best-practices.md)
- **用途**: 格式化脚本设计原则和最佳实践
- **内容**:
  - 脚本架构设计
  - Maven集成方式
  - 性能优化
  - 故障排除
- **适用人群**: 工具开发者、DevOps工程师

#### [Lombok集成指南](lombok-guide.md)
- **用途**: Lombok库使用指南和最佳实践
- **内容**:
  - 注解详解和使用示例
  - 最佳实践和避免事项
  - IDE配置和故障排查
  - 版本兼容性说明
- **适用人群**: Java开发者

## 🚀 快速开始

### 新开发者入门
1. 阅读 [金融产品搜索API指南](financial-product-search-guide.md) 了解核心功能
2. 配置开发环境，参考 [代码格式化指南](code-formatting-guide.md)
3. 了解日志系统，参考 [Log4j2日志配置指南](logging-guide.md)

### 运维人员部署
1. 阅读 [Elasticsearch配置现代化迁移指南](elasticsearch-config-migration-guide.md)
2. 配置日志收集，参考 [Log4j2日志配置指南](logging-guide.md)
3. 设置监控和健康检查

### API集成开发
1. 查看 [金融产品搜索API指南](financial-product-search-guide.md) 的API接口部分
2. 参考Java客户端和cURL示例
3. 了解搜索最佳实践和性能优化

## 📖 文档维护

### 更新原则
- **及时性**: 代码变更后及时更新相关文档
- **准确性**: 确保文档内容与实际实现一致
- **完整性**: 覆盖所有重要功能和配置
- **易读性**: 使用清晰的结构和示例

### 贡献指南
1. **新增文档**: 在相应目录创建Markdown文件
2. **更新索引**: 在本文件中添加新文档的链接和说明
3. **格式规范**: 遵循现有文档的格式和风格
4. **审查流程**: 通过Pull Request提交文档变更

## 🔗 相关链接

### 外部资源
- [Google Java Style Guide](https://google.github.io/styleguide/javaguide.html)
- [Elasticsearch官方文档](https://www.elastic.co/guide/en/elasticsearch/reference/current/index.html)
- [Log4j2官方文档](https://logging.apache.org/log4j/2.x/)
- [Spring Boot官方文档](https://spring.io/projects/spring-boot)

### 项目资源
- [项目源码](../src/)
- [配置文件](../src/main/resources/)
- [测试用例](../src/test/)
- [构建脚本](../scripts/)

## 📞 支持和反馈

如果您在使用文档过程中遇到问题或有改进建议，请：

1. **提交Issue**: 在项目仓库中创建Issue
2. **Pull Request**: 直接提交文档改进
3. **团队讨论**: 在团队会议中提出文档相关问题

---

**最后更新**: 2024年1月
**维护者**: 开发团队
