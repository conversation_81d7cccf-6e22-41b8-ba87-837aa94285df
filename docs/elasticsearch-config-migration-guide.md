# Elasticsearch配置现代化迁移指南

本文档详细说明了从废弃的Elasticsearch API迁移到现代API的完整过程。

## 🔄 迁移概述

### 迁移前后对比

| 项目 | 迁移前 | 迁移后 |
|------|--------|--------|
| **客户端** | RestHighLevelClient (废弃) | ElasticsearchClient (推荐) |
| **配置方式** | 手动配置 | 类型安全的配置属性 |
| **连接管理** | 基础连接 | 完整的连接池管理 |
| **认证支持** | 基本认证 | 多种认证方式 |
| **SSL支持** | 简单SSL | 完整的SSL/TLS配置 |
| **健康检查** | 无 | 内置健康检查 |
| **配置验证** | 运行时错误 | 编译时类型检查 |

## 🚨 识别的废弃API

### 1. RestHighLevelClient
```java
// ❌ 废弃的API
@Bean
public RestHighLevelClient elasticsearchClient() {
    // 这个客户端在Elasticsearch 7.15+中已被标记为废弃
}
```

### 2. RestClients.create()
```java
// ❌ 废弃的创建方式
return RestClients.create(clientConfiguration).rest();
```

### 3. 简单的配置方式
```java
// ❌ 缺乏类型安全和验证
@Value("${spring.elasticsearch.uris:http://localhost:9200}")
private String elasticsearchUrl;
```

## ✅ 现代化解决方案

### 1. 新的ElasticsearchClient

```java
// ✅ 推荐的新客户端
@Bean
public ElasticsearchClient elasticsearchClient(RestClient restClient) {
    ElasticsearchTransport transport = new RestClientTransport(
            restClient, new JacksonJsonpMapper());
    return new ElasticsearchClient(transport);
}
```

**优势:**
- 官方推荐的新客户端
- 更好的类型安全
- 支持异步操作
- 更好的错误处理

### 2. 类型安全的配置属性

```java
// ✅ 类型安全的配置
@Data
@ConfigurationProperties(prefix = "spring.elasticsearch")
public class ElasticsearchProperties {
    private List<String> uris = Arrays.asList("http://localhost:9200");
    private Authentication authentication = new Authentication();
    private Connection connection = new Connection();
    // ...
}
```

**优势:**
- IDE自动完成支持
- 编译时类型检查
- 配置验证
- 更好的文档化

### 3. 完整的连接池配置

```java
// ✅ 完整的连接池管理
builder.setHttpClientConfigCallback(httpClientBuilder -> 
        httpClientBuilder
                .setMaxConnTotal(maxConnections)
                .setMaxConnPerRoute(maxConnectionsPerRoute)
                .setDefaultCredentialsProvider(credentialsProvider));
```

**优势:**
- 性能优化
- 连接复用
- 资源管理
- 故障恢复

## 📋 迁移步骤

### 步骤1: 添加新依赖

在`pom.xml`中添加新的Elasticsearch Java客户端：

```xml
<!-- 新的Elasticsearch Java客户端 -->
<dependency>
    <groupId>co.elastic.clients</groupId>
    <artifactId>elasticsearch-java</artifactId>
    <version>7.17.15</version>
</dependency>

<!-- Jackson JSON处理器 -->
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
</dependency>
```

### 步骤2: 创建配置属性类

创建`ElasticsearchProperties.java`：

```java
@Data
@Component
@ConfigurationProperties(prefix = "spring.elasticsearch")
public class ElasticsearchProperties {
    private List<String> uris = Arrays.asList("http://localhost:9200");
    private Authentication authentication = new Authentication();
    private Connection connection = new Connection();
    private Ssl ssl = new Ssl();
    private Pool pool = new Pool();
    
    // 内部配置类...
}
```

### 步骤3: 更新配置类

替换`ElasticsearchConfig.java`中的废弃API：

```java
@Configuration
@EnableElasticsearchRepositories(basePackages = "com.howbuy.ai.repository")
public class ElasticsearchConfig extends ElasticsearchConfiguration {
    
    @Autowired
    private ElasticsearchProperties elasticsearchProperties;
    
    @Override
    public ClientConfiguration clientConfiguration() {
        // 使用新的配置方式
    }
    
    @Bean
    @Primary
    public RestClient restClient() {
        // 创建低级别客户端
    }
    
    @Bean
    public ElasticsearchClient elasticsearchClient(RestClient restClient) {
        // 创建新的高级客户端
    }
}
```

### 步骤4: 更新配置文件

创建`application-elasticsearch.yml`：

```yaml
spring:
  elasticsearch:
    uris:
      - http://localhost:9200
    authentication:
      username: ${ES_USERNAME:}
      password: ${ES_PASSWORD:}
    connection:
      connect-timeout: 10s
      socket-timeout: 30s
    ssl:
      enabled: false
    pool:
      max-connections: 100
      max-connections-per-route: 10
```

### 步骤5: 验证迁移

运行测试确保功能正常：

```bash
mvn clean test
```

## 🔧 配置选项详解

### 连接配置

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `connect-timeout` | 10s | 连接超时时间 |
| `socket-timeout` | 30s | Socket读取超时 |
| `request-timeout` | 60s | 请求超时时间 |
| `keep-alive` | 30s | 连接保持活跃时间 |

### 连接池配置

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `max-connections` | 100 | 最大连接数 |
| `max-connections-per-route` | 10 | 每路由最大连接数 |
| `idle-timeout` | 5m | 连接空闲超时 |
| `time-to-live` | 30m | 连接生存时间 |

### SSL配置

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `enabled` | false | 是否启用SSL |
| `verify-hostnames` | true | 是否验证主机名 |
| `truststore` | - | 信任存储路径 |
| `keystore` | - | 密钥存储路径 |

## 🌟 新功能特性

### 1. 多节点集群支持

```yaml
spring:
  elasticsearch:
    uris:
      - http://es-node1:9200
      - http://es-node2:9200
      - http://es-node3:9200
```

### 2. 环境特定配置

```yaml
---
spring:
  config:
    activate:
      on-profile: prod
  elasticsearch:
    uris:
      - https://prod-es-1.example.com:9200
      - https://prod-es-2.example.com:9200
    ssl:
      enabled: true
```

### 3. 健康检查

```java
@Bean
public ElasticsearchHealthIndicator elasticsearchHealthIndicator(
        ElasticsearchClient elasticsearchClient) {
    return new ElasticsearchHealthIndicator(elasticsearchClient);
}
```

### 4. 性能监控

配置类自动记录连接性能和配置信息：

```
2024-01-01 10:00:00 INFO  - Elasticsearch客户端配置完成，连接节点: [localhost:9200]
2024-01-01 10:00:00 INFO  - RestClient创建完成，最大连接数: 100, 每路由最大连接数: 10
```

## 🚀 最佳实践

### 1. 生产环境配置

```yaml
spring:
  elasticsearch:
    uris:
      - https://es-cluster.prod.com:9200
    authentication:
      username: ${ES_USERNAME}
      password: ${ES_PASSWORD}
    ssl:
      enabled: true
      verify-hostnames: true
    pool:
      max-connections: 200
      max-connections-per-route: 20
    connection:
      connect-timeout: 5s
      socket-timeout: 60s
```

### 2. 开发环境配置

```yaml
spring:
  elasticsearch:
    uris:
      - http://localhost:9200
    authentication:
      username: ""
      password: ""
    ssl:
      enabled: false
```

### 3. 监控和日志

```yaml
logging:
  level:
    com.howbuy.ai.config: DEBUG
    org.elasticsearch: INFO
    co.elastic.clients: INFO

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,elasticsearch
```

## ✅ 迁移验证清单

- [ ] 新依赖已添加到pom.xml
- [ ] ElasticsearchProperties类已创建
- [ ] ElasticsearchConfig类已更新
- [ ] 配置文件已更新
- [ ] 所有测试通过
- [ ] 应用启动正常
- [ ] Elasticsearch连接正常
- [ ] 搜索功能正常
- [ ] 性能监控正常
- [ ] 日志输出正常

## 🔍 故障排除

### 常见问题

1. **编译错误**: 确保Java 8兼容性
2. **连接失败**: 检查URI配置和网络连接
3. **认证失败**: 验证用户名密码配置
4. **SSL错误**: 检查证书配置

### 调试技巧

1. 启用DEBUG日志查看详细信息
2. 使用健康检查端点验证连接
3. 检查Elasticsearch集群状态
4. 验证网络连接和防火墙设置

## 📈 性能提升

迁移后的性能改进：

- **连接复用**: 减少连接开销
- **连接池管理**: 更好的资源利用
- **异步支持**: 提高并发性能
- **类型安全**: 减少运行时错误
- **配置验证**: 提前发现配置问题

通过这次迁移，您的Elasticsearch配置将更加现代化、可靠和高性能！
