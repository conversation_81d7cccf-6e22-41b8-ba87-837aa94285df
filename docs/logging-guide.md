# Log4j2 日志配置指南

本项目使用Log4j2作为日志框架，提供高性能的异步日志记录和灵活的配置选项。

## 日志配置概述

### 主要特性

- **异步日志记录**: 使用Disruptor提供高性能异步日志
- **多种输出格式**: 支持控制台、文件、JSON格式
- **日志分级**: 不同级别的日志输出到不同文件
- **自动滚动**: 按时间和大小自动滚动日志文件
- **性能监控**: 专门的性能日志记录
- **结构化日志**: JSON格式便于日志收集和分析

### 日志文件结构

```
logs/
├── word-search-service.log          # 主应用日志
├── word-search-service-error.log    # 错误日志
├── word-search-service-perf.log     # 性能日志
└── word-search-service-json.log     # JSON格式日志
```

## 日志级别配置

### 应用程序日志
- **DEBUG**: 详细的调试信息
- **INFO**: 一般信息，如方法调用、业务流程
- **WARN**: 警告信息，如性能问题
- **ERROR**: 错误信息，如异常和失败操作

### 第三方库日志
- **Spring框架**: INFO级别
- **Elasticsearch**: DEBUG级别（开发环境）
- **HTTP客户端**: WARN级别
- **Tomcat**: INFO级别

## 性能日志

### 自动性能监控

项目集成了性能监控功能，自动记录：

1. **API请求性能**: 每个REST API的响应时间
2. **搜索操作性能**: Elasticsearch搜索的执行时间
3. **方法执行时间**: 关键业务方法的性能
4. **慢操作检测**: 超过阈值的操作自动告警

### 性能阈值

- **慢操作**: 执行时间 > 1000ms
- **性能警告**: 执行时间 > 500ms
- **API慢响应**: 响应时间 > 2000ms

### 使用示例

```java
// 方法性能监控
PerformanceLogger.PerformanceMonitor monitor = 
    new PerformanceLogger.PerformanceMonitor("methodName", params);
try {
    // 业务逻辑
    monitor.endWithResult(result);
} catch (Exception e) {
    monitor.end();
    throw e;
}

// 搜索性能记录
PerformanceLogger.logSearchPerformance("keyword", keyword, resultCount, executionTime);

// API性能记录
PerformanceLogger.logApiPerformance(endpoint, method, statusCode, executionTime);
```

## 日志格式

### 控制台输出格式
```
2024-06-11 20:30:15.123 [http-nio-8080-exec-1] INFO  c.h.ai.controller.WordSearchController - 搜索请求 - 关键词: Spring
```

### 文件输出格式
```
2024-06-11 20:30:15.123 [http-nio-8080-exec-1] INFO  com.howbuy.ai.controller.WordSearchController - 搜索请求 - 关键词: Spring
```

### JSON输出格式
```json
{
  "@timestamp": "2024-06-11T20:30:15.123+08:00",
  "log.level": "INFO",
  "message": "搜索请求 - 关键词: Spring",
  "process.thread.name": "http-nio-8080-exec-1",
  "log.logger": "com.howbuy.ai.controller.WordSearchController",
  "labels": {
    "application": "word-search-service",
    "environment": "production"
  }
}
```

## 配置文件

### 主配置文件: log4j2-spring.xml
- 生产环境使用
- 完整的日志配置
- 多种输出格式
- 自动滚动策略

### 测试配置文件: log4j2-test.xml
- 测试环境使用
- 简化的配置
- 主要输出到控制台
- 减少日志噪音

## 环境变量配置

可以通过环境变量调整日志配置：

```bash
# 设置日志级别
export LOG_LEVEL_ROOT=INFO
export LOG_LEVEL_APP=DEBUG

# 设置日志文件路径
export LOG_HOME=/var/log/word-search-service

# 设置环境标识
export SPRING_PROFILES_ACTIVE=production
```

## 日志收集集成

### ELK Stack集成
JSON格式的日志可以直接被Logstash收集：

```yaml
# logstash.conf
input {
  file {
    path => "/path/to/logs/word-search-service-json.log"
    codec => "json"
  }
}

filter {
  # 可以添加额外的过滤器
}

output {
  elasticsearch {
    hosts => ["localhost:9200"]
    index => "word-search-service-logs-%{+YYYY.MM.dd}"
  }
}
```

### Fluentd集成
```xml
<source>
  @type tail
  path /path/to/logs/word-search-service-json.log
  pos_file /var/log/fluentd/word-search-service.log.pos
  tag word-search-service
  format json
</source>

<match word-search-service>
  @type elasticsearch
  host localhost
  port 9200
  index_name word-search-service-logs
</match>
```

## 最佳实践

### 1. 日志级别使用
- **DEBUG**: 仅在开发和调试时使用
- **INFO**: 记录重要的业务流程和状态变化
- **WARN**: 记录可能的问题，但不影响正常运行
- **ERROR**: 记录错误和异常，需要关注和处理

### 2. 性能日志
- 对关键业务方法进行性能监控
- 设置合理的性能阈值
- 定期分析性能日志，优化慢操作

### 3. 结构化日志
- 使用一致的日志格式
- 包含足够的上下文信息
- 便于日志分析和问题排查

### 4. 日志安全
- 避免记录敏感信息（密码、令牌等）
- 对用户输入进行适当的脱敏处理
- 定期清理过期的日志文件

## 故障排查

### 常见问题

1. **日志文件不生成**
   - 检查日志目录权限
   - 确认Log4j2配置文件路径正确

2. **性能下降**
   - 检查是否启用了异步日志
   - 调整日志级别，减少不必要的日志输出

3. **磁盘空间不足**
   - 检查日志滚动策略
   - 调整日志保留天数
   - 启用日志压缩

### 调试命令

```bash
# 查看日志文件
tail -f logs/word-search-service.log

# 查看错误日志
tail -f logs/word-search-service-error.log

# 查看性能日志
tail -f logs/word-search-service-perf.log

# 搜索特定关键词
grep "ERROR" logs/word-search-service.log

# 分析JSON日志
jq '.' logs/word-search-service-json.log | head -10
```
