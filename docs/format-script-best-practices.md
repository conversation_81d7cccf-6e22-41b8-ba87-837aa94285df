# 🚀 格式化脚本最佳实践

## 📋 设计原则

`scripts/format-code.sh` 脚本遵循以下最佳实践原则：

### 1. **单一职责原则**
- 专注于代码格式化功能
- 使用Maven插件作为唯一实现方式
- 避免多种实现方式带来的复杂性

### 2. **依赖管理最佳实践**
- 完全依赖Maven生态系统
- 版本信息集中在pom.xml中管理
- 避免手动下载和管理JAR文件

### 3. **简洁性原则**
- 移除不必要的备用方案
- 代码结构清晰简洁
- 易于理解和维护

## 🎯 脚本架构

### 核心组件

```bash
# 1. 环境检查
check_maven() {
    # 验证Maven环境
    # 检查项目结构
}

# 2. 格式化功能
format_code() {
    # 使用Maven插件格式化
    mvn ${FMT_MAVEN_PLUGIN}:format
}

# 3. 检查功能
check_format() {
    # 使用Maven插件检查
    mvn ${FMT_MAVEN_PLUGIN}:check
}
```

### 设计优势

| 特性 | 实现方式 | 优势 |
|------|----------|------|
| **依赖管理** | Maven插件 | 自动化、可靠 |
| **版本控制** | pom.xml集中管理 | 一致性、易维护 |
| **错误处理** | Maven内置 | 专业、详细 |
| **平台兼容** | Java生态系统 | 跨平台支持 |

## 📊 与传统方式对比

### ❌ 传统JAR方式问题
```bash
# 问题1：路径硬编码
GOOGLE_JAVA_FORMAT_JAR="$HOME/.m2/repository/..."

# 问题2：手动依赖管理
if [ ! -f "$JAR_FILE" ]; then
    wget https://github.com/...
fi

# 问题3：版本不同步
GOOGLE_JAVA_FORMAT_VERSION="1.7"  # 可能与pom.xml不一致
```

### ✅ Maven插件方式优势
```bash
# 优势1：简洁的配置
FMT_MAVEN_PLUGIN="com.coveo:fmt-maven-plugin:2.9"

# 优势2：自动依赖管理
mvn ${FMT_MAVEN_PLUGIN}:format  # Maven自动处理依赖

# 优势3：版本一致性
# 版本信息来自pom.xml，确保一致性
```

## 🔧 使用指南

### 基本命令

```bash
# 格式化代码
./scripts/format-code.sh format

# 检查格式
./scripts/format-code.sh check

# 查看帮助
./scripts/format-code.sh help
```

### 集成到工作流

#### 1. **开发阶段**
```bash
# 编写代码后格式化
./scripts/format-code.sh format
```

#### 2. **提交前检查**
```bash
# 提交前验证格式
./scripts/format-code.sh check
```

#### 3. **CI/CD集成**
```yaml
# GitHub Actions
- name: Check code format
  run: ./scripts/format-code.sh check
```

## 🎯 最佳实践建议

### 1. **团队协作**
- 所有团队成员使用相同的脚本
- 统一的格式化标准
- 减少代码审查中的格式争议

### 2. **自动化集成**
```bash
# Git pre-commit hook
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
./scripts/format-code.sh check
if [ $? -ne 0 ]; then
    echo "格式检查失败，请运行: ./scripts/format-code.sh format"
    exit 1
fi
EOF
chmod +x .git/hooks/pre-commit
```

### 3. **IDE集成**
- 配置IDE使用相同的Google Java Style
- 保存时自动格式化
- 与脚本保持一致性

## 📋 环境要求

### 必需条件
- ✅ Java 8+
- ✅ Maven 3.x
- ✅ 在项目根目录运行

### 自动检查
脚本会自动验证：
- Maven是否安装
- 是否在正确的项目目录
- pom.xml是否存在

## 🚀 性能特点

### 执行效率
- **快速启动**: 直接使用Maven插件
- **并行处理**: Maven自动优化
- **增量处理**: 只处理需要的文件

### 资源使用
- **内存效率**: Maven管理内存使用
- **磁盘空间**: 不需要额外的JAR文件
- **网络使用**: 依赖缓存机制

## 🔍 故障排除

### 常见问题

#### 1. **Maven未找到**
```bash
# 错误信息
Maven not found. Please install Maven first.

# 解决方案
# 安装Maven或检查PATH环境变量
```

#### 2. **不在项目根目录**
```bash
# 错误信息
pom.xml not found. Please run from project root.

# 解决方案
cd /path/to/project/root
./scripts/format-code.sh format
```

#### 3. **格式检查失败**
```bash
# 错误信息
代码格式检查失败

# 解决方案
./scripts/format-code.sh format  # 自动修复格式
```

## 📚 相关文档

- [代码格式化指南](code-formatting-guide.md)
- [Google Java Style Guide](https://google.github.io/styleguide/javaguide.html)
- [fmt-maven-plugin文档](https://github.com/coveooss/fmt-maven-plugin)

## 🎉 总结

这个脚本体现了现代Java项目的最佳实践：

1. **简洁性** - 专注核心功能，避免过度设计
2. **可靠性** - 依赖成熟的Maven生态系统
3. **一致性** - 与项目构建系统完全集成
4. **易用性** - 简单的命令接口，清晰的错误信息

通过遵循这些最佳实践，我们创建了一个既强大又简洁的代码格式化工具。
