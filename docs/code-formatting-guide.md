# 代码格式化指南

本项目使用 **Google Java Format** 来确保代码风格的一致性，遵循 [Google Java Style Guide](https://google.github.io/styleguide/javaguide.html)。

## 🎯 为什么使用Google Java Format？

- **一致性**: 确保整个团队使用相同的代码风格
- **自动化**: 减少代码审查中关于格式的讨论
- **可读性**: 提高代码的可读性和维护性
- **标准化**: 遵循业界广泛认可的Google Java Style Guide

## 🛠️ 工具配置

### Maven插件

项目已配置以下Maven插件：

1. **fmt-maven-plugin**: 用于格式化和检查代码格式
2. **maven-checkstyle-plugin**: 用于代码风格检查

### 依赖版本

- Google Java Format: `1.7`
- fmt-maven-plugin: `2.9`
- Checkstyle: `9.3`

## 📋 使用方法

### 1. Maven命令

#### 格式化代码
```bash
# 格式化所有Java文件
mvn fmt:format

# 只格式化src/main/java下的文件
mvn fmt:format -Dfmt.sourceDirectory=src/main/java
```

#### 检查代码格式
```bash
# 检查代码格式是否符合Google Java Style
mvn fmt:check

# 运行Checkstyle检查
mvn checkstyle:check
```

### 2. 脚本命令

项目提供了便捷的脚本 `scripts/format-code.sh`：

```bash
# 格式化所有Java文件
./scripts/format-code.sh format

# 检查代码格式
./scripts/format-code.sh check

# 使用Maven插件格式化（同format）
./scripts/format-code.sh maven-format

# 使用Maven插件检查（同check）
./scripts/format-code.sh maven-check

# 显示帮助信息
./scripts/format-code.sh help
```

### 3. IDE集成

#### IntelliJ IDEA

1. **安装Google Java Format插件**:
   - File → Settings → Plugins
   - 搜索 "google-java-format"
   - 安装并重启IDE

2. **启用插件**:
   - File → Settings → google-java-format Settings
   - 勾选 "Enable google-java-format"

3. **导入代码风格**:
   - 项目已包含 `.idea/codeStyles/` 配置
   - IDE会自动使用项目的代码风格设置

#### VS Code

1. **安装Java扩展包**:
   - Extension Pack for Java
   - Language Support for Java(TM) by Red Hat

2. **配置已自动设置**:
   - 项目包含 `.vscode/settings.json` 配置
   - 自动使用Google Java Style

#### Eclipse

1. **下载Google Style配置**:
   ```bash
   wget https://raw.githubusercontent.com/google/styleguide/gh-pages/eclipse-java-google-style.xml
   ```

2. **导入配置**:
   - Window → Preferences → Java → Code Style → Formatter
   - Import → 选择下载的XML文件

## 🔧 配置文件说明

### .editorconfig
定义了跨编辑器的基本格式设置：
- 字符编码: UTF-8
- 行结束符: LF
- 缩进: 2个空格
- 行长度限制: 100字符

### pom.xml配置

#### fmt-maven-plugin配置
```xml
<plugin>
    <groupId>com.coveo</groupId>
    <artifactId>fmt-maven-plugin</artifactId>
    <version>2.9</version>
    <configuration>
        <style>google</style>
        <verbose>true</verbose>
    </configuration>
</plugin>
```

#### Checkstyle配置
```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-checkstyle-plugin</artifactId>
    <configuration>
        <configLocation>google_checks.xml</configLocation>
        <encoding>UTF-8</encoding>
        <consoleOutput>true</consoleOutput>
    </configuration>
</plugin>
```

## 🚀 CI/CD集成

### GitHub Actions

项目包含 `.github/workflows/code-format-check.yml` 工作流：

1. **自动检查**: 每次push和PR都会检查代码格式
2. **格式化建议**: PR中会自动格式化代码（如果需要）
3. **Checkstyle报告**: 生成详细的代码风格检查报告

### 本地Git Hooks

可以设置pre-commit hook来在提交前自动检查格式：

```bash
# 创建pre-commit hook
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
echo "Running code format check..."
./scripts/format-code.sh maven-check
if [ $? -ne 0 ]; then
    echo "Code format check failed. Please run './scripts/format-code.sh maven-format' to fix."
    exit 1
fi
EOF

# 设置执行权限
chmod +x .git/hooks/pre-commit
```

## 📏 代码风格规则

### 主要规则

1. **缩进**: 使用2个空格，不使用Tab
2. **行长度**: 最大100字符
3. **大括号**: 使用K&R风格
4. **导入**: 按字母顺序排列，避免使用通配符
5. **空行**: 类、方法之间使用空行分隔

### 示例

#### ✅ 正确格式
```java
public class FinancialProduct {
  private String tscode;
  private String tsname;

  public FinancialProduct(String tscode, String tsname) {
    this.tscode = tscode;
    this.tsname = tsname;
  }

  public boolean isValid() {
    return StringUtils.hasText(tscode) && StringUtils.hasText(tsname);
  }
}
```

#### ❌ 错误格式
```java
public class FinancialProduct{
    private String tscode;
    private String tsname;
    public FinancialProduct(String tscode,String tsname){
        this.tscode=tscode;
        this.tsname=tsname;
    }
    public boolean isValid(){
        return StringUtils.hasText(tscode)&&StringUtils.hasText(tsname);
    }
}
```

## 🔍 常见问题

### Q: 格式化后代码看起来很奇怪？
A: Google Java Style可能与你习惯的风格不同，但它是经过深思熟虑的标准。坚持使用一段时间后会习惯。

### Q: 可以自定义格式化规则吗？
A: 不建议修改Google Java Format的规则，这样可以保持与业界标准的一致性。

### Q: IDE格式化与Maven插件结果不一致？
A: 确保IDE使用了正确的Google Java Style配置，并且版本与Maven插件一致。

### Q: 如何处理长行？
A: Google Java Format会自动处理长行的换行，遵循其算法即可。

## 🎯 快速开始

### 基本使用

```bash
# 检查代码格式
./scripts/format-code.sh check

# 格式化所有Java文件
./scripts/format-code.sh format

# 显示帮助信息
./scripts/format-code.sh help
```

### IDE集成

#### IntelliJ IDEA
1. 安装 "google-java-format" 插件
2. 启用插件: Settings → google-java-format Settings → Enable
3. 项目已包含代码风格配置，会自动应用

#### VS Code
1. 安装 Java Extension Pack
2. 项目已配置自动格式化设置
3. 保存时自动格式化

### Git Hooks (推荐)
```bash
# 设置pre-commit hook
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
./scripts/format-code.sh check
if [ $? -ne 0 ]; then
    echo "代码格式检查失败，请运行 './scripts/format-code.sh format' 修复"
    exit 1
fi
EOF
chmod +x .git/hooks/pre-commit
```

## 📊 版本兼容性

| 组件 | 版本 | Java兼容性 |
|------|------|------------|
| Google Java Format | 1.7 | Java 8+ |
| fmt-maven-plugin | 2.9 | Java 8+ |
| Checkstyle | 9.3 | Java 8+ |

## 📚 参考资料

- [Google Java Style Guide](https://google.github.io/styleguide/javaguide.html)
- [Google Java Format GitHub](https://github.com/google/google-java-format)
- [fmt-maven-plugin](https://github.com/coveooss/fmt-maven-plugin)
- [Checkstyle](https://checkstyle.sourceforge.io/)

## 🤝 团队协作

1. **新成员**: 加入项目时请先配置IDE的代码格式化设置
2. **代码审查**: 关注代码逻辑，格式问题由工具自动处理
3. **提交前**: 运行 `./scripts/format-code.sh check` 确保格式正确
4. **持续改进**: 如有格式化相关问题，请及时反馈和讨论
