# Lombok 集成指南

本项目已成功集成Lombok库，遵循Java开发最佳实践，提供简洁、高效的代码编写体验。

## Lombok 概述

Lombok是一个Java库，通过注解自动生成常用的样板代码（如getter、setter、toString、equals、hashCode等），显著减少代码量并提高开发效率。

### 主要优势

- **减少样板代码**: 自动生成getter/setter、构造函数等
- **提高代码可读性**: 专注于业务逻辑，减少冗余代码
- **编译时处理**: 注解在编译时处理，运行时无性能影响
- **IDE支持**: 主流IDE都有Lombok插件支持

## 项目中的Lombok配置

### Maven依赖配置

```xml
<properties>
    <lombok.version>1.18.24</lombok.version>
</properties>

<dependencies>
    <!-- Lombok -->
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
        <scope>provided</scope>
    </dependency>
</dependencies>
```

### 编译器配置

```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-compiler-plugin</artifactId>
    <version>3.8.1</version>
    <configuration>
        <source>1.8</source>
        <target>1.8</target>
        <annotationProcessorPaths>
            <path>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </path>
        </annotationProcessorPaths>
    </configuration>
</plugin>
```

## 项目中使用的Lombok注解

### 1. 实体类 (WordDocument)

```java
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(exclude = {"createTime", "updateTime", "viewCount"})
@ToString(exclude = {"content"}) // 排除content字段避免日志过长
@Document(indexName = "word_documents")
public class WordDocument {
    // 字段定义...
    
    @Builder.Default
    private Integer viewCount = 0;
    
    @Builder.Default
    private LocalDateTime createTime = LocalDateTime.now();
}
```

**最佳实践说明**:
- ✅ **避免@Data**: 在实体类上使用@Getter/@Setter而不是@Data，避免潜在的性能问题
- ✅ **@EqualsAndHashCode(exclude)**: 排除时间戳和计数器字段，避免比较问题
- ✅ **@ToString(exclude)**: 排除大文本字段，避免日志过长
- ✅ **@Builder.Default**: 为需要默认值的字段提供默认值

### 2. DTO类 (SearchRequest, ApiResponse)

```java
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SearchRequest {
    @NotBlank(message = "搜索关键词不能为空")
    private String keyword;
    
    @Builder.Default
    private int page = 0;
    
    @Builder.Default
    private int size = 10;
    
    // 其他字段...
}
```

**DTO类特点**:
- ✅ **@Data**: DTO类可以安全使用@Data，因为它们通常是简单的数据传输对象
- ✅ **@Builder**: 提供流畅的对象创建API
- ✅ **Bean Validation兼容**: Lombok注解与Bean Validation注解完美兼容

### 3. 服务类和控制器 (@Slf4j)

```java
@Slf4j
@Service
public class WordSearchService {
    
    public Page<WordDocument> searchByKeyword(String keyword, int page, int size) {
        log.info("关键词搜索: {}", keyword);
        // 业务逻辑...
    }
}
```

**日志最佳实践**:
- ✅ **@Slf4j**: 自动生成`log`字段，支持SLF4J API
- ✅ **统一日志**: 替代手动创建Logger，保持代码一致性
- ✅ **Log4j2兼容**: 与项目的Log4j2配置完美兼容

## Lombok注解详解

### 核心注解

| 注解 | 功能 | 使用场景 |
|------|------|----------|
| `@Getter` | 生成getter方法 | 所有需要访问器的类 |
| `@Setter` | 生成setter方法 | 可变对象 |
| `@Data` | 生成getter/setter/toString/equals/hashCode | DTO、简单数据类 |
| `@NoArgsConstructor` | 生成无参构造函数 | JPA实体、框架要求 |
| `@AllArgsConstructor` | 生成全参构造函数 | 不可变对象 |
| `@Builder` | 生成建造者模式 | 复杂对象创建 |

### 高级注解

| 注解 | 功能 | 最佳实践 |
|------|------|----------|
| `@EqualsAndHashCode` | 生成equals和hashCode | 使用exclude排除不适合的字段 |
| `@ToString` | 生成toString方法 | 使用exclude避免敏感信息和循环引用 |
| `@Slf4j` | 生成日志字段 | 替代手动创建Logger |
| `@Builder.Default` | 设置Builder默认值 | 为字段提供默认值 |

## 使用示例

### 1. 创建对象 - Builder模式

```java
// 使用Builder创建WordDocument
WordDocument document = WordDocument.builder()
    .title("Spring Boot教程")
    .content("详细的Spring Boot学习指南...")
    .category("技术文档")
    .author("张三")
    .tags(new String[]{"Spring", "Java", "教程"})
    .build();

// 使用Builder创建SearchRequest
SearchRequest request = SearchRequest.builder()
    .keyword("Spring Boot")
    .page(0)
    .size(20)
    .build();
```

### 2. 日志记录

```java
@Slf4j
@RestController
public class WordSearchController {
    
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Page<WordDocument>>> search(
            @RequestParam String keyword) {
        
        log.info("搜索请求 - 关键词: {}", keyword);
        
        try {
            // 业务逻辑...
            log.debug("搜索完成，找到 {} 条结果", results.getTotalElements());
            return ResponseEntity.ok(ApiResponse.success("搜索成功", results));
        } catch (Exception e) {
            log.error("搜索失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("搜索失败: " + e.getMessage()));
        }
    }
}
```

## 最佳实践总结

### ✅ 推荐做法

1. **实体类使用@Getter/@Setter**: 避免@Data可能带来的问题
2. **合理使用exclude**: 在@EqualsAndHashCode和@ToString中排除不适合的字段
3. **DTO类使用@Data**: 简单数据传输对象可以安全使用@Data
4. **统一使用@Slf4j**: 替代手动创建Logger，保持代码一致性
5. **Builder模式**: 对于复杂对象，使用@Builder提供流畅的创建API

### ❌ 避免做法

1. **实体类使用@Data**: 可能导致性能问题和意外的equals/hashCode行为
2. **忽略exclude**: 不排除不适合的字段可能导致循环引用或性能问题
3. **过度使用@AllArgsConstructor**: 参数过多的构造函数难以维护
4. **混合日志框架**: 不要在同一个项目中混合使用不同的日志创建方式

## IDE配置

### IntelliJ IDEA

1. 安装Lombok插件：`File` → `Settings` → `Plugins` → 搜索"Lombok"
2. 启用注解处理：`File` → `Settings` → `Build` → `Compiler` → `Annotation Processors` → 勾选"Enable annotation processing"

### Eclipse

1. 下载lombok.jar
2. 运行：`java -jar lombok.jar`
3. 选择Eclipse安装目录并安装

### VS Code

1. 安装Java扩展包
2. 安装Lombok Annotations Support插件

## 故障排查

### 常见问题

1. **编译错误"找不到符号"**
   - 检查IDE是否安装Lombok插件
   - 确认注解处理器已启用
   - 重新导入Maven项目

2. **运行时找不到方法**
   - 确认Lombok依赖scope为provided
   - 检查编译器配置中的注解处理器路径

3. **IDE中代码标红**
   - 重启IDE
   - 重新构建项目
   - 检查Lombok插件版本兼容性

### 调试技巧

```bash
# 查看Lombok生成的代码
mvn clean compile
javap -cp target/classes com.howbuy.ai.entity.WordDocument

# 编译时显示详细信息
mvn clean compile -X
```

## 版本兼容性

- **Java 8+**: Lombok 1.18.24完全支持
- **Spring Boot 2.7.x**: 完美兼容
- **Maven 3.6+**: 推荐版本
- **IDE插件**: 使用最新版本以获得最佳体验

通过遵循这些最佳实践，您可以充分利用Lombok的优势，编写更简洁、更易维护的Java代码。
