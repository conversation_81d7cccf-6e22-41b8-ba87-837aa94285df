#!/bin/bash

# API测试脚本
BASE_URL="http://localhost:8080/api"

echo "=== Word Search Service API 测试 ==="
echo ""

# 检查服务是否运行
echo "1. 检查服务健康状态..."
if curl -s "$BASE_URL/actuator/health" | grep -q "UP"; then
    echo "✅ 服务运行正常"
else
    echo "❌ 服务未运行，请先启动应用"
    exit 1
fi

echo ""

# 测试获取所有文档
echo "2. 获取所有文档..."
curl -s "$BASE_URL/v1/search/all?page=0&size=5" | jq '.' || echo "获取文档列表"

echo ""

# 测试关键词搜索
echo "3. 测试关键词搜索 (Spring)..."
curl -s "$BASE_URL/v1/search?keyword=Spring&page=0&size=5" | jq '.' || echo "搜索Spring相关文档"

echo ""

# 测试分类查找
echo "4. 测试分类查找 (技术文档)..."
curl -s "$BASE_URL/v1/search/category/技术文档?page=0&size=5" | jq '.' || echo "查找技术文档分类"

echo ""

# 测试创建新文档
echo "5. 测试创建新文档..."
NEW_DOC='{
  "title": "测试文档",
  "content": "这是一个通过API创建的测试文档，包含Spring Boot和Elasticsearch相关内容。",
  "category": "测试分类",
  "author": "API测试",
  "tags": ["测试", "API", "Spring Boot"]
}'

CREATED_DOC=$(curl -s -X POST "$BASE_URL/v1/documents" \
  -H "Content-Type: application/json" \
  -d "$NEW_DOC")

echo "$CREATED_DOC" | jq '.' || echo "创建文档响应: $CREATED_DOC"

# 提取文档ID
DOC_ID=$(echo "$CREATED_DOC" | jq -r '.data.id' 2>/dev/null)

echo ""

if [ "$DOC_ID" != "null" ] && [ -n "$DOC_ID" ]; then
    echo "6. 测试获取文档详情 (ID: $DOC_ID)..."
    curl -s "$BASE_URL/v1/search/document/$DOC_ID" | jq '.' || echo "获取文档详情"
    
    echo ""
    
    echo "7. 测试更新文档..."
    UPDATE_DOC='{
      "title": "更新后的测试文档",
      "content": "这是更新后的文档内容，添加了更多关于Elasticsearch的信息。",
      "category": "更新分类",
      "author": "API测试更新"
    }'
    
    curl -s -X PUT "$BASE_URL/v1/documents/$DOC_ID" \
      -H "Content-Type: application/json" \
      -d "$UPDATE_DOC" | jq '.' || echo "更新文档"
    
    echo ""
    
    echo "8. 测试高级搜索..."
    SEARCH_REQUEST='{
      "keyword": "测试",
      "page": 0,
      "size": 5
    }'
    
    curl -s -X POST "$BASE_URL/v1/search/advanced" \
      -H "Content-Type: application/json" \
      -d "$SEARCH_REQUEST" | jq '.' || echo "高级搜索"
    
    echo ""
    
    echo "9. 测试删除文档..."
    curl -s -X DELETE "$BASE_URL/v1/documents/$DOC_ID" | jq '.' || echo "删除文档"
    
else
    echo "⚠️  无法获取文档ID，跳过后续测试"
fi

echo ""
echo "=== API测试完成 ==="
