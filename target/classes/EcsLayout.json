{"@timestamp": {"$resolver": "timestamp", "pattern": {"format": "yyyy-MM-dd'T'HH:mm:ss.SSSZZZ", "timeZone": "Asia/Shanghai"}}, "log.level": {"$resolver": "level", "field": "name"}, "message": {"$resolver": "message", "stringified": true}, "process.thread.name": {"$resolver": "thread", "field": "name"}, "log.logger": {"$resolver": "logger", "field": "name"}, "labels": {"application": "word-search-service", "environment": "${env:SPRING_PROFILES_ACTIVE:-default}"}, "error.type": {"$resolver": "exception", "field": "className"}, "error.message": {"$resolver": "exception", "field": "message"}, "error.stack_trace": {"$resolver": "exception", "field": "stackTrace", "stackTrace": {"stringified": true}}}