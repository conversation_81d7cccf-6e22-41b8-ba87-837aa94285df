# Spring Boot应用配置
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: word-search-service
  
  # Elasticsearch配置
  elasticsearch:
    uris: http://localhost:9200
    username: 
    password: 
    connection-timeout: 10s
    socket-timeout: 30s
  
  # Jackson配置
  jackson:
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai

# 日志配置 (使用Log4j2，配置在log4j2-spring.xml中)

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when_authorized

# 自定义配置
word-search:
  elasticsearch:
    index-name: word_documents
    max-results: 100
    highlight-enabled: true
