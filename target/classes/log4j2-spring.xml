<?xml version="1.0" encoding="UTF-8"?>
<!--
    Log4j2配置文件
    提供高性能的异步日志记录和灵活的日志格式配置
-->
<Configuration status="WARN" monitorInterval="30">
    
    <!-- 属性配置 -->
    <Properties>
        <!-- 日志文件路径 -->
        <Property name="LOG_HOME">./logs</Property>
        <!-- 应用名称 -->
        <Property name="APP_NAME">word-search-service</Property>
        <!-- 日志格式 - 控制台 -->
        <Property name="CONSOLE_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %highlight{%-5level} %style{%logger{36}}{cyan} - %msg%n</Property>
        <!-- 日志格式 - 文件 -->
        <Property name="FILE_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</Property>
        <!-- JSON格式 - 用于日志收集系统 -->
        <Property name="JSON_PATTERN">{"timestamp":"%d{yyyy-MM-dd'T'HH:mm:ss.SSSZ}","level":"%level","thread":"%t","logger":"%logger","message":"%msg","exception":"%ex"}%n</Property>
    </Properties>
    
    <!-- Appenders配置 -->
    <Appenders>
        
        <!-- 控制台输出 -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${CONSOLE_PATTERN}"/>
            <!-- 只输出INFO及以上级别的日志到控制台 -->
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
        </Console>
        
        <!-- 应用日志文件 - 按日期和大小滚动 -->
        <RollingFile name="AppFile" fileName="${LOG_HOME}/${APP_NAME}.log"
                     filePattern="${LOG_HOME}/${APP_NAME}-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${FILE_PATTERN}"/>
            <Policies>
                <!-- 每天滚动 -->
                <TimeBasedTriggeringPolicy/>
                <!-- 文件大小超过100MB时滚动 -->
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <!-- 保留30天的日志文件 -->
            <DefaultRolloverStrategy max="30"/>
        </RollingFile>
        
        <!-- 错误日志文件 - 只记录ERROR级别 -->
        <RollingFile name="ErrorFile" fileName="${LOG_HOME}/${APP_NAME}-error.log"
                     filePattern="${LOG_HOME}/${APP_NAME}-error-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${FILE_PATTERN}"/>
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
        </RollingFile>
        
        <!-- 性能日志文件 - 用于记录慢查询等性能相关日志 -->
        <RollingFile name="PerfFile" fileName="${LOG_HOME}/${APP_NAME}-perf.log"
                     filePattern="${LOG_HOME}/${APP_NAME}-perf-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${FILE_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="15"/>
        </RollingFile>
        
        <!-- JSON格式日志文件 - 用于日志收集系统 -->
        <RollingFile name="JsonFile" fileName="${LOG_HOME}/${APP_NAME}-json.log"
                     filePattern="${LOG_HOME}/${APP_NAME}-json-%d{yyyy-MM-dd}-%i.log.gz">
            <JsonTemplateLayout eventTemplateUri="classpath:EcsLayout.json"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="15"/>
        </RollingFile>
        
        <!-- 异步Appender - 提高性能 -->
        <AsyncLogger name="AsyncAppFile" additivity="false">
            <AppenderRef ref="AppFile"/>
            <AppenderRef ref="ErrorFile"/>
        </AsyncLogger>
        
    </Appenders>
    
    <!-- Loggers配置 -->
    <Loggers>
        
        <!-- 应用程序日志 -->
        <Logger name="com.howbuy.ai" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AppFile"/>
            <AppenderRef ref="ErrorFile"/>
            <AppenderRef ref="JsonFile"/>
        </Logger>
        
        <!-- 性能日志 -->
        <Logger name="com.howbuy.ai.performance" level="INFO" additivity="false">
            <AppenderRef ref="PerfFile"/>
        </Logger>
        
        <!-- Spring框架日志 -->
        <Logger name="org.springframework" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AppFile"/>
        </Logger>
        
        <!-- Elasticsearch日志 -->
        <Logger name="org.springframework.data.elasticsearch" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AppFile"/>
        </Logger>
        
        <Logger name="org.elasticsearch" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AppFile"/>
        </Logger>
        
        <!-- HTTP客户端日志 -->
        <Logger name="org.apache.http" level="WARN" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AppFile"/>
        </Logger>
        
        <!-- Tomcat日志 -->
        <Logger name="org.apache.catalina" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AppFile"/>
        </Logger>
        
        <!-- HikariCP连接池日志 -->
        <Logger name="com.zaxxer.hikari" level="WARN" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AppFile"/>
        </Logger>
        
        <!-- 根日志配置 -->
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AppFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Root>
        
    </Loggers>
    
</Configuration>
