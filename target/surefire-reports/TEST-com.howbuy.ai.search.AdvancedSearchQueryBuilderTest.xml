<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.howbuy.ai.search.AdvancedSearchQueryBuilderTest" time="0.305" tests="1" errors="0" skipped="0" failures="0">
  <properties>
    <property name="gopherProxySet" value="false"/>
    <property name="awt.toolkit" value="sun.lwawt.macosx.LWCToolkit"/>
    <property name="socksProxyHost" value="127.0.0.1"/>
    <property name="http.proxyHost" value="127.0.0.1"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="sun.cpu.isalist" value=""/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/workspace/javatemp/word-search-service/target/test-classes:/Users/<USER>/workspace/javatemp/word-search-service/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.7.18/spring-boot-starter-web-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.7.18/spring-boot-starter-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.7.18/spring-boot-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.7.18/spring-boot-autoconfigure-2.7.18.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.7.18/spring-boot-starter-json-2.7.18.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.13.5/jackson-datatype-jdk8-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.13.5/jackson-datatype-jsr310-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.13.5/jackson-module-parameter-names-2.13.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.7.18/spring-boot-starter-tomcat-2.7.18.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.83/tomcat-embed-core-9.0.83.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.83/tomcat-embed-websocket-9.0.83.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.31/spring-web-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.31/spring-beans-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.31/spring-webmvc-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.31/spring-aop-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.31/spring-context-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.31/spring-expression-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-elasticsearch/2.7.18/spring-boot-starter-data-elasticsearch-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-elasticsearch/4.4.18/spring-data-elasticsearch-4.4.18.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.31/spring-tx-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.7.18/spring-data-commons-2.7.18.jar:/Users/<USER>/.m2/repository/org/elasticsearch/client/elasticsearch-rest-high-level-client/7.17.15/elasticsearch-rest-high-level-client-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch/7.17.15/elasticsearch-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-core/7.17.15/elasticsearch-core-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-secure-sm/7.17.15/elasticsearch-secure-sm-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-x-content/7.17.15/elasticsearch-x-content-7.17.15.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.13.5/jackson-dataformat-smile-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.13.5/jackson-dataformat-cbor-2.13.5.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-geo/7.17.15/elasticsearch-geo-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-lz4/7.17.15/elasticsearch-lz4-7.17.15.jar:/Users/<USER>/.m2/repository/org/lz4/lz4-java/1.8.0/lz4-java-1.8.0.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-core/8.11.1/lucene-core-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-analyzers-common/8.11.1/lucene-analyzers-common-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-backward-codecs/8.11.1/lucene-backward-codecs-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-grouping/8.11.1/lucene-grouping-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-highlighter/8.11.1/lucene-highlighter-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-join/8.11.1/lucene-join-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-memory/8.11.1/lucene-memory-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-misc/8.11.1/lucene-misc-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-queries/8.11.1/lucene-queries-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-queryparser/8.11.1/lucene-queryparser-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-sandbox/8.11.1/lucene-sandbox-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-spatial3d/8.11.1/lucene-spatial3d-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-suggest/8.11.1/lucene-suggest-8.11.1.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-cli/7.17.15/elasticsearch-cli-7.17.15.jar:/Users/<USER>/.m2/repository/net/sf/jopt-simple/jopt-simple/5.0.2/jopt-simple-5.0.2.jar:/Users/<USER>/.m2/repository/com/carrotsearch/hppc/0.8.1/hppc-0.8.1.jar:/Users/<USER>/.m2/repository/joda-time/joda-time/2.10.10/joda-time-2.10.10.jar:/Users/<USER>/.m2/repository/com/tdunning/t-digest/3.2/t-digest-3.2.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-plugin-classloader/7.17.15/elasticsearch-plugin-classloader-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/mapper-extras-client/7.17.15/mapper-extras-client-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/parent-join-client/7.17.15/parent-join-client-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/aggs-matrix-stats-client/7.17.15/aggs-matrix-stats-client-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/rank-eval-client/7.17.15/rank-eval-client-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/lang-mustache-client/7.17.15/lang-mustache-client-7.17.15.jar:/Users/<USER>/.m2/repository/com/github/spullara/mustache/java/compiler/0.9.6/compiler-0.9.6.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/co/elastic/clients/elasticsearch-java/7.17.15/elasticsearch-java-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/client/elasticsearch-rest-client/7.17.15/elasticsearch-rest-client-7.17.15.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4.1.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.16/httpcore-nio-4.4.16.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/jakarta/json/jakarta.json-api/1.1.6/jakarta.json-api-1.1.6.jar:/Users/<USER>/.m2/repository/org/eclipse/parsson/parsson/1.0.0/parsson-1.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.5/jackson-databind-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.5/jackson-annotations-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.5/jackson-core-2.13.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.7.18/spring-boot-starter-validation-2.7.18.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.83/tomcat-embed-el-9.0.83.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.2.5.Final/hibernate-validator-6.2.5.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.3.Final/jboss-logging-3.4.3.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.7.18/spring-boot-starter-actuator-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.7.18/spring-boot-actuator-autoconfigure-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.7.18/spring-boot-actuator-2.7.18.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.9.17/micrometer-core-1.9.17.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-configuration-processor/2.7.18/spring-boot-configuration-processor-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-log4j2/2.7.18/spring-boot-starter-log4j2-2.7.18.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-slf4j-impl/2.17.2/log4j-slf4j-impl-2.17.2.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.17.2/log4j-api-2.17.2.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-core/2.17.2/log4j-core-2.17.2.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-jul/2.17.2/log4j-jul-2.17.2.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.36/jul-to-slf4j-1.7.36.jar:/Users/<USER>/.m2/repository/com/lmax/disruptor/3.4.4/disruptor-3.4.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.13.5/jackson-dataformat-yaml-2.13.5.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.30/snakeyaml-1.30.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.24/lombok-1.18.24.jar:/Users/<USER>/.m2/repository/com/google/googlejavaformat/google-java-format/1.7/google-java-format-1.7.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/27.0.1-jre/guava-27.0.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/2.5.2/checker-qual-2.5.2.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.2.0/error_prone_annotations-2.2.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.1/j2objc-annotations-1.1.jar:/Users/<USER>/.m2/repository/org/codehaus/mojo/animal-sniffer-annotations/1.17/animal-sniffer-annotations-1.17.jar:/Users/<USER>/.m2/repository/com/google/errorprone/javac-shaded/9+181-r4173-1/javac-shaded-9+181-r4173-1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/2.7.18/spring-boot-starter-test-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.7.18/spring-boot-test-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/2.7.18/spring-boot-test-autoconfigure-2.7.18.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.7.0/json-path-2.7.0.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.4.11/json-smart-2.4.11.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.4.11/accessors-smart-2.4.11.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.3/asm-9.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.22.0/assertj-core-3.22.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.8.2/junit-jupiter-5.8.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.8.2/junit-jupiter-params-5.8.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.8.2/junit-jupiter-engine-5.8.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.8.2/junit-platform-engine-1.8.2.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/4.5.1/mockito-core-4.5.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.12.23/byte-buddy-1.12.23.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.12.23/byte-buddy-agent-1.12.23.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/4.5.1/mockito-junit-jupiter-4.5.1.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.31/spring-core-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.31/spring-jcl-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.3.31/spring-test-5.3.31.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar:/Users/<USER>/.m2/repository/org/testcontainers/elasticsearch/1.17.6/elasticsearch-1.17.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/testcontainers/1.17.6/testcontainers-1.17.6.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.22/commons-compress-1.22.jar:/Users/<USER>/.m2/repository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-api/3.2.13/docker-java-api-3.2.13.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport-zerodep/3.2.13/docker-java-transport-zerodep-3.2.13.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport/3.2.13/docker-java-transport-3.2.13.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.8.0/jna-5.8.0.jar:/Users/<USER>/.m2/repository/org/testcontainers/junit-jupiter/1.17.6/junit-jupiter-1.17.6.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.8.2/junit-jupiter-api-5.8.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.2.0/opentest4j-1.2.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.8.2/junit-platform-commons-1.8.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:"/>
    <property name="https.proxyPort" value="7890"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="user.timezone" value=""/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="/Users/<USER>/sdk/jdk1.8.0_431.jdk/Contents/Home/jre/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/workspace/javatemp/word-search-service/target/surefire/surefirebooter639334461719752906.jar /Users/<USER>/workspace/javatemp/word-search-service/target/surefire 2025-06-12T17-35-58_757-jvmRun1 surefire4630730148387054676tmp surefire_03330771677033674617tmp"/>
    <property name="http.nonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/10|*.**********/10|********/8|*.********/8|localhost|*.localhost|local|*.local|***********/16|*.***********/16|*********/4|*.*********/4|240.0.0.0/4|*.240.0.0.0/4"/>
    <property name="test" value="AdvancedSearchQueryBuilderTest#testSlopValidation"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/workspace/javatemp/word-search-service/target/test-classes:/Users/<USER>/workspace/javatemp/word-search-service/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.7.18/spring-boot-starter-web-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.7.18/spring-boot-starter-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.7.18/spring-boot-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.7.18/spring-boot-autoconfigure-2.7.18.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.7.18/spring-boot-starter-json-2.7.18.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.13.5/jackson-datatype-jdk8-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.13.5/jackson-datatype-jsr310-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.13.5/jackson-module-parameter-names-2.13.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.7.18/spring-boot-starter-tomcat-2.7.18.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.83/tomcat-embed-core-9.0.83.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.83/tomcat-embed-websocket-9.0.83.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.31/spring-web-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.31/spring-beans-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.31/spring-webmvc-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.31/spring-aop-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.31/spring-context-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.31/spring-expression-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-elasticsearch/2.7.18/spring-boot-starter-data-elasticsearch-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-elasticsearch/4.4.18/spring-data-elasticsearch-4.4.18.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.31/spring-tx-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.7.18/spring-data-commons-2.7.18.jar:/Users/<USER>/.m2/repository/org/elasticsearch/client/elasticsearch-rest-high-level-client/7.17.15/elasticsearch-rest-high-level-client-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch/7.17.15/elasticsearch-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-core/7.17.15/elasticsearch-core-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-secure-sm/7.17.15/elasticsearch-secure-sm-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-x-content/7.17.15/elasticsearch-x-content-7.17.15.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.13.5/jackson-dataformat-smile-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.13.5/jackson-dataformat-cbor-2.13.5.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-geo/7.17.15/elasticsearch-geo-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-lz4/7.17.15/elasticsearch-lz4-7.17.15.jar:/Users/<USER>/.m2/repository/org/lz4/lz4-java/1.8.0/lz4-java-1.8.0.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-core/8.11.1/lucene-core-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-analyzers-common/8.11.1/lucene-analyzers-common-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-backward-codecs/8.11.1/lucene-backward-codecs-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-grouping/8.11.1/lucene-grouping-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-highlighter/8.11.1/lucene-highlighter-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-join/8.11.1/lucene-join-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-memory/8.11.1/lucene-memory-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-misc/8.11.1/lucene-misc-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-queries/8.11.1/lucene-queries-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-queryparser/8.11.1/lucene-queryparser-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-sandbox/8.11.1/lucene-sandbox-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-spatial3d/8.11.1/lucene-spatial3d-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-suggest/8.11.1/lucene-suggest-8.11.1.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-cli/7.17.15/elasticsearch-cli-7.17.15.jar:/Users/<USER>/.m2/repository/net/sf/jopt-simple/jopt-simple/5.0.2/jopt-simple-5.0.2.jar:/Users/<USER>/.m2/repository/com/carrotsearch/hppc/0.8.1/hppc-0.8.1.jar:/Users/<USER>/.m2/repository/joda-time/joda-time/2.10.10/joda-time-2.10.10.jar:/Users/<USER>/.m2/repository/com/tdunning/t-digest/3.2/t-digest-3.2.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-plugin-classloader/7.17.15/elasticsearch-plugin-classloader-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/mapper-extras-client/7.17.15/mapper-extras-client-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/parent-join-client/7.17.15/parent-join-client-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/aggs-matrix-stats-client/7.17.15/aggs-matrix-stats-client-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/rank-eval-client/7.17.15/rank-eval-client-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/lang-mustache-client/7.17.15/lang-mustache-client-7.17.15.jar:/Users/<USER>/.m2/repository/com/github/spullara/mustache/java/compiler/0.9.6/compiler-0.9.6.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/co/elastic/clients/elasticsearch-java/7.17.15/elasticsearch-java-7.17.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/client/elasticsearch-rest-client/7.17.15/elasticsearch-rest-client-7.17.15.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4.1.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.16/httpcore-nio-4.4.16.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/jakarta/json/jakarta.json-api/1.1.6/jakarta.json-api-1.1.6.jar:/Users/<USER>/.m2/repository/org/eclipse/parsson/parsson/1.0.0/parsson-1.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.5/jackson-databind-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.5/jackson-annotations-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.5/jackson-core-2.13.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.7.18/spring-boot-starter-validation-2.7.18.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.83/tomcat-embed-el-9.0.83.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.2.5.Final/hibernate-validator-6.2.5.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.3.Final/jboss-logging-3.4.3.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.7.18/spring-boot-starter-actuator-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.7.18/spring-boot-actuator-autoconfigure-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.7.18/spring-boot-actuator-2.7.18.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.9.17/micrometer-core-1.9.17.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-configuration-processor/2.7.18/spring-boot-configuration-processor-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-log4j2/2.7.18/spring-boot-starter-log4j2-2.7.18.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-slf4j-impl/2.17.2/log4j-slf4j-impl-2.17.2.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.17.2/log4j-api-2.17.2.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-core/2.17.2/log4j-core-2.17.2.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-jul/2.17.2/log4j-jul-2.17.2.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.36/jul-to-slf4j-1.7.36.jar:/Users/<USER>/.m2/repository/com/lmax/disruptor/3.4.4/disruptor-3.4.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.13.5/jackson-dataformat-yaml-2.13.5.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.30/snakeyaml-1.30.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.24/lombok-1.18.24.jar:/Users/<USER>/.m2/repository/com/google/googlejavaformat/google-java-format/1.7/google-java-format-1.7.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/27.0.1-jre/guava-27.0.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/2.5.2/checker-qual-2.5.2.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.2.0/error_prone_annotations-2.2.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.1/j2objc-annotations-1.1.jar:/Users/<USER>/.m2/repository/org/codehaus/mojo/animal-sniffer-annotations/1.17/animal-sniffer-annotations-1.17.jar:/Users/<USER>/.m2/repository/com/google/errorprone/javac-shaded/9+181-r4173-1/javac-shaded-9+181-r4173-1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/2.7.18/spring-boot-starter-test-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.7.18/spring-boot-test-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/2.7.18/spring-boot-test-autoconfigure-2.7.18.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.7.0/json-path-2.7.0.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.4.11/json-smart-2.4.11.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.4.11/accessors-smart-2.4.11.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.3/asm-9.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.22.0/assertj-core-3.22.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.8.2/junit-jupiter-5.8.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.8.2/junit-jupiter-params-5.8.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.8.2/junit-jupiter-engine-5.8.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.8.2/junit-platform-engine-1.8.2.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/4.5.1/mockito-core-4.5.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.12.23/byte-buddy-1.12.23.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.12.23/byte-buddy-agent-1.12.23.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/4.5.1/mockito-junit-jupiter-4.5.1.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.31/spring-core-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.31/spring-jcl-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.3.31/spring-test-5.3.31.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar:/Users/<USER>/.m2/repository/org/testcontainers/elasticsearch/1.17.6/elasticsearch-1.17.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/testcontainers/1.17.6/testcontainers-1.17.6.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.22/commons-compress-1.22.jar:/Users/<USER>/.m2/repository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-api/3.2.13/docker-java-api-3.2.13.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport-zerodep/3.2.13/docker-java-transport-zerodep-3.2.13.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport/3.2.13/docker-java-transport-3.2.13.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.8.0/jna-5.8.0.jar:/Users/<USER>/.m2/repository/org/testcontainers/junit-jupiter/1.17.6/junit-jupiter-1.17.6.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.8.2/junit-jupiter-api-5.8.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.2.0/opentest4j-1.2.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.8.2/junit-platform-commons-1.8.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.home" value="/Users/<USER>/sdk/jdk1.8.0_431.jdk/Contents/Home/jre"/>
    <property name="basedir" value="/Users/<USER>/workspace/javatemp/word-search-service"/>
    <property name="https.proxyHost" value="127.0.0.1"/>
    <property name="file.separator" value="/"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.CGraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/workspace/javatemp/word-search-service/target/surefire/surefirebooter639334461719752906.jar"/>
    <property name="sun.boot.class.path" value="/Users/<USER>/sdk/jdk1.8.0_431.jdk/Contents/Home/jre/lib/resources.jar:/Users/<USER>/sdk/jdk1.8.0_431.jdk/Contents/Home/jre/lib/rt.jar:/Users/<USER>/sdk/jdk1.8.0_431.jdk/Contents/Home/jre/lib/jsse.jar:/Users/<USER>/sdk/jdk1.8.0_431.jdk/Contents/Home/jre/lib/jce.jar:/Users/<USER>/sdk/jdk1.8.0_431.jdk/Contents/Home/jre/lib/charsets.jar:/Users/<USER>/sdk/jdk1.8.0_431.jdk/Contents/Home/jre/lib/jfr.jar:/Users/<USER>/sdk/jdk1.8.0_431.jdk/Contents/Home/jre/classes"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/10|*.**********/10|********/8|*.********/8|localhost|*.localhost|local|*.local|***********/16|*.***********/16|*********/4|*.*********/4|240.0.0.0/4|*.240.0.0.0/4"/>
    <property name="java.runtime.version" value="1.8.0_431-b10"/>
    <property name="user.name" value="iceman"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.endorsed.dirs" value="/Users/<USER>/sdk/jdk1.8.0_431.jdk/Contents/Home/jre/lib/endorsed"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/wh/1m_13zbs7zldk9ct7c6tcfrw0000gn/T/"/>
    <property name="java.version" value="1.8.0_431"/>
    <property name="user.dir" value="/Users/<USER>/workspace/javatemp/word-search-service"/>
    <property name="os.arch" value="aarch64"/>
    <property name="socksProxyPort" value="7890"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.lwawt.macosx.CPrinterJob"/>
    <property name="sun.os.patch.level" value="unknown"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="25.431-b10"/>
    <property name="java.specification.maintenance.version" value="6"/>
    <property name="java.ext.dirs" value="/Users/<USER>/Library/Java/Extensions:/Users/<USER>/sdk/jdk1.8.0_431.jdk/Contents/Home/jre/lib/ext:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="java.class.version" value="52.0"/>
    <property name="socksNonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/10|*.**********/10|********/8|*.********/8|localhost|*.localhost|local|*.local|***********/16|*.***********/16|*********/4|*.*********/4|240.0.0.0/4|*.240.0.0.0/4"/>
    <property name="http.proxyPort" value="7890"/>
  </properties>
  <testcase name="testSlopValidation" classname="com.howbuy.ai.search.AdvancedSearchQueryBuilderTest" time="0.301"/>
</testsuite>