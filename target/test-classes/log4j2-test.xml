<?xml version="1.0" encoding="UTF-8"?>
<!--
    Log4j2测试环境配置文件
    简化的日志配置，主要输出到控制台
-->
<Configuration status="WARN">
    
    <Properties>
        <Property name="CONSOLE_PATTERN">%d{HH:mm:ss.SSS} [%t] %highlight{%-5level} %style{%logger{36}}{cyan} - %msg%n</Property>
    </Properties>
    
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${CONSOLE_PATTERN}"/>
        </Console>
    </Appenders>
    
    <Loggers>
        <!-- 应用程序日志 - 测试时使用DEBUG级别 -->
        <Logger name="com.howbuy.ai" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        
        <!-- Spring框架日志 - 测试时减少输出 -->
        <Logger name="org.springframework" level="WARN" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        
        <!-- Elasticsearch日志 - 测试时减少输出 -->
        <Logger name="org.springframework.data.elasticsearch" level="WARN" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        
        <Logger name="org.elasticsearch" level="WARN" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        
        <!-- 根日志配置 -->
        <Root level="WARN">
            <AppenderRef ref="Console"/>
        </Root>
    </Loggers>
    
</Configuration>
