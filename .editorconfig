# EditorConfig配置文件
# 确保不同编辑器和IDE使用一致的代码风格
# 更多信息: https://editorconfig.org

# 根配置文件
root = true

# 所有文件的默认设置
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# Java文件设置 (遵循Google Java Style)
[*.java]
indent_style = space
indent_size = 2
max_line_length = 100
continuation_indent_size = 4

# XML文件设置 (Maven POM等)
[*.xml]
indent_style = space
indent_size = 4

# YAML文件设置 (Spring Boot配置等)
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Properties文件设置
[*.properties]
indent_style = space
indent_size = 4

# JSON文件设置
[*.json]
indent_style = space
indent_size = 2

# Markdown文件设置
[*.md]
trim_trailing_whitespace = false
max_line_length = off

# Shell脚本设置
[*.sh]
indent_style = space
indent_size = 2

# Dockerfile设置
[Dockerfile*]
indent_style = space
indent_size = 2
