#!/bin/bash

# Google Java Format 代码格式化脚本
# 使用说明：
#   ./scripts/format-code.sh          - 格式化所有Java文件
#   ./scripts/format-code.sh check    - 检查代码格式
#   ./scripts/format-code.sh fix      - 修复代码格式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# Google Java Format JAR文件路径
GOOGLE_JAVA_FORMAT_VERSION="1.15.0"
GOOGLE_JAVA_FORMAT_JAR="$HOME/.m2/repository/com/google/googlejavaformat/google-java-format/${GOOGLE_JAVA_FORMAT_VERSION}/google-java-format-${GOOGLE_JAVA_FORMAT_VERSION}-all-deps.jar"

# 检查Google Java Format JAR是否存在
check_google_java_format() {
    if [ ! -f "$GOOGLE_JAVA_FORMAT_JAR" ]; then
        echo -e "${YELLOW}Google Java Format JAR not found. Downloading...${NC}"
        mvn dependency:get -Dartifact=com.google.googlejavaformat:google-java-format:${GOOGLE_JAVA_FORMAT_VERSION}:jar:all-deps
        
        if [ ! -f "$GOOGLE_JAVA_FORMAT_JAR" ]; then
            echo -e "${RED}Failed to download Google Java Format JAR${NC}"
            exit 1
        fi
    fi
}

# 查找所有Java文件
find_java_files() {
    find src -name "*.java" -type f
}

# 格式化代码
format_code() {
    echo -e "${BLUE}正在格式化Java代码...${NC}"
    
    local java_files
    java_files=$(find_java_files)
    
    if [ -z "$java_files" ]; then
        echo -e "${YELLOW}没有找到Java文件${NC}"
        return 0
    fi
    
    echo "$java_files" | xargs java -jar "$GOOGLE_JAVA_FORMAT_JAR" --replace
    
    echo -e "${GREEN}代码格式化完成！${NC}"
}

# 检查代码格式
check_format() {
    echo -e "${BLUE}正在检查Java代码格式...${NC}"
    
    local java_files
    java_files=$(find_java_files)
    
    if [ -z "$java_files" ]; then
        echo -e "${YELLOW}没有找到Java文件${NC}"
        return 0
    fi
    
    local unformatted_files
    unformatted_files=$(echo "$java_files" | xargs java -jar "$GOOGLE_JAVA_FORMAT_JAR" --dry-run --set-exit-if-changed 2>/dev/null || echo "$java_files" | xargs java -jar "$GOOGLE_JAVA_FORMAT_JAR" --dry-run | diff -u /dev/stdin <(cat $java_files) | grep "^+++" | sed 's/^+++ //' || true)
    
    if [ -n "$unformatted_files" ]; then
        echo -e "${RED}以下文件格式不符合Google Java Style:${NC}"
        echo "$unformatted_files"
        echo -e "${YELLOW}运行 './scripts/format-code.sh fix' 来修复格式问题${NC}"
        return 1
    else
        echo -e "${GREEN}所有Java文件格式正确！${NC}"
        return 0
    fi
}

# 使用Maven插件格式化
maven_format() {
    echo -e "${BLUE}使用Maven插件格式化代码...${NC}"
    mvn fmt:format
    echo -e "${GREEN}Maven格式化完成！${NC}"
}

# 使用Maven插件检查格式
maven_check() {
    echo -e "${BLUE}使用Maven插件检查代码格式...${NC}"
    if mvn fmt:check; then
        echo -e "${GREEN}所有文件格式正确！${NC}"
        return 0
    else
        echo -e "${RED}代码格式检查失败${NC}"
        echo -e "${YELLOW}运行 'mvn fmt:format' 或 './scripts/format-code.sh fix' 来修复格式问题${NC}"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "Google Java Format 代码格式化工具"
    echo ""
    echo "用法:"
    echo "  $0 [命令]"
    echo ""
    echo "命令:"
    echo "  format, fix    格式化所有Java文件"
    echo "  check          检查代码格式是否符合Google Java Style"
    echo "  maven-format   使用Maven插件格式化代码"
    echo "  maven-check    使用Maven插件检查代码格式"
    echo "  help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 format      # 格式化所有Java文件"
    echo "  $0 check       # 检查代码格式"
    echo "  $0 maven-check # 使用Maven检查格式"
}

# 主函数
main() {
    local command="${1:-format}"
    
    case "$command" in
        "format"|"fix")
            check_google_java_format
            format_code
            ;;
        "check")
            check_google_java_format
            check_format
            ;;
        "maven-format")
            maven_format
            ;;
        "maven-check")
            maven_check
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            echo -e "${RED}未知命令: $command${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
