#!/bin/bash

# Google Java Format 代码格式化脚本
# 使用说明：
#   ./scripts/format-code.sh          - 格式化所有Java文件
#   ./scripts/format-code.sh check    - 检查代码格式
#   ./scripts/format-code.sh fix      - 修复代码格式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# Maven插件配置
FMT_MAVEN_PLUGIN="com.coveo:fmt-maven-plugin:2.9"

# 检查Maven是否可用
check_maven() {
    if ! command -v mvn &> /dev/null; then
        echo -e "${RED}Maven not found. Please install Maven first.${NC}"
        exit 1
    fi

    # 检查是否在Maven项目根目录
    if [ ! -f "pom.xml" ]; then
        echo -e "${RED}pom.xml not found. Please run this script from the project root directory.${NC}"
        exit 1
    fi

    echo -e "${GREEN}Maven environment verified.${NC}"
}

# 查找所有Java文件
find_java_files() {
    find src -name "*.java" -type f
}

# 格式化代码
format_code() {
    echo -e "${BLUE}正在使用Maven插件格式化Java代码...${NC}"

    local java_files
    java_files=$(find_java_files)

    if [ -z "$java_files" ]; then
        echo -e "${YELLOW}没有找到Java文件${NC}"
        return 0
    fi

    if mvn ${FMT_MAVEN_PLUGIN}:format; then
        echo -e "${GREEN}代码格式化完成！${NC}"
    else
        echo -e "${RED}代码格式化失败${NC}"
        return 1
    fi
}

# 检查代码格式
check_format() {
    echo -e "${BLUE}正在使用Maven插件检查Java代码格式...${NC}"

    local java_files
    java_files=$(find_java_files)

    if [ -z "$java_files" ]; then
        echo -e "${YELLOW}没有找到Java文件${NC}"
        return 0
    fi

    if mvn ${FMT_MAVEN_PLUGIN}:check; then
        echo -e "${GREEN}所有Java文件格式正确！${NC}"
        return 0
    else
        echo -e "${RED}代码格式检查失败${NC}"
        echo -e "${YELLOW}运行 './scripts/format-code.sh fix' 来修复格式问题${NC}"
        return 1
    fi
}

# 使用Maven插件格式化
maven_format() {
    echo -e "${BLUE}使用Maven插件格式化代码...${NC}"
    if mvn com.coveo:fmt-maven-plugin:2.9:format; then
        echo -e "${GREEN}Maven格式化完成！${NC}"
    else
        echo -e "${RED}Maven格式化失败${NC}"
        return 1
    fi
}

# 使用Maven插件检查格式
maven_check() {
    echo -e "${BLUE}使用Maven插件检查代码格式...${NC}"
    if mvn com.coveo:fmt-maven-plugin:2.9:check; then
        echo -e "${GREEN}所有文件格式正确！${NC}"
        return 0
    else
        echo -e "${RED}代码格式检查失败${NC}"
        echo -e "${YELLOW}运行 'mvn com.coveo:fmt-maven-plugin:2.9:format' 或 './scripts/format-code.sh fix' 来修复格式问题${NC}"
        return 1
    fi
}



# 显示帮助信息
show_help() {
    echo "Google Java Format 代码格式化工具"
    echo ""
    echo "用法:"
    echo "  $0 <命令>"
    echo ""
    echo "可用命令:"
    echo "  format, fix    格式化所有Java文件"
    echo "  check          检查代码格式是否符合Google Java Style"
    echo "  maven-format   使用Maven插件格式化代码（同format）"
    echo "  maven-check    使用Maven插件检查代码格式（同check）"
    echo "  help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 format      # 格式化所有Java文件"
    echo "  $0 check       # 检查代码格式"
    echo "  $0 help        # 显示帮助信息"
    echo ""
    echo "注意:"
    echo "  - 必须提供命令参数"
    echo "  - 使用Maven插件进行格式化和检查"
    echo "  - 确保在项目根目录运行此脚本"
    echo "  - 需要Maven环境支持"
}

# 主函数
main() {
    local command="${1:-help}"

    # 如果没有提供参数，显示帮助信息
    if [ $# -eq 0 ]; then
        show_help
        return 0
    fi

    # 检查Maven环境（仅在需要时）
    case "$command" in
        "format"|"fix"|"check"|"maven-format"|"maven-check")
            check_maven
            ;;
    esac

    case "$command" in
        "format"|"fix")
            format_code
            ;;
        "check")
            check_format
            ;;
        "maven-format")
            maven_format
            ;;
        "maven-check")
            maven_check
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            echo -e "${RED}未知命令: $command${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
